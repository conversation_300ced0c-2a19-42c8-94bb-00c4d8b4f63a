"""
操作录制回放模块
"""
import json
import time
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

import pyautogui
from pynput import mouse, keyboard

from logger_config import logger
from instruction_parser import Action, ActionType
from automation_executor import AutomationExecutor
from screen_parser import ScreenParser

class RecordingState(Enum):
    """录制状态"""
    STOPPED = "stopped"
    RECORDING = "recording"
    PAUSED = "paused"

@dataclass
class RecordedEvent:
    """录制的事件"""
    timestamp: float
    event_type: str
    data: Dict[str, Any]
    screenshot_path: Optional[str] = None

@dataclass
class RecordingSession:
    """录制会话"""
    name: str
    description: str
    created_at: datetime
    duration: float
    events: List[RecordedEvent]
    metadata: Dict[str, Any]

class ActionRecorder:
    """操作录制器"""
    
    def __init__(self):
        self.state = RecordingState.STOPPED
        self.events: List[RecordedEvent] = []
        self.start_time: Optional[float] = None
        self.session_name: Optional[str] = None
        
        # 监听器
        self.mouse_listener: Optional[mouse.Listener] = None
        self.keyboard_listener: Optional[keyboard.Listener] = None
        
        # 录制配置
        self.record_screenshots = True
        self.screenshot_interval = 2.0  # 每2秒截图一次
        self.last_screenshot_time = 0
        
        # 屏幕解析器
        self.screen_parser = ScreenParser()
        
        # 录制目录
        self.recordings_dir = Path("recordings")
        self.recordings_dir.mkdir(exist_ok=True)
        
        # 回调函数
        self.on_event_recorded: Optional[Callable] = None
    
    def start_recording(self, session_name: str = None):
        """开始录制"""
        if self.state == RecordingState.RECORDING:
            logger.warning("录制已在进行中")
            return
        
        self.session_name = session_name or f"session_{int(time.time())}"
        self.events.clear()
        self.start_time = time.time()
        self.last_screenshot_time = 0
        self.state = RecordingState.RECORDING
        
        # 启动监听器
        self._start_listeners()
        
        # 初始截图
        if self.record_screenshots:
            self._take_screenshot("session_start")
        
        logger.info(f"开始录制会话: {self.session_name}")
    
    def pause_recording(self):
        """暂停录制"""
        if self.state == RecordingState.RECORDING:
            self.state = RecordingState.PAUSED
            self._stop_listeners()
            logger.info("录制已暂停")
    
    def resume_recording(self):
        """恢复录制"""
        if self.state == RecordingState.PAUSED:
            self.state = RecordingState.RECORDING
            self._start_listeners()
            logger.info("录制已恢复")
    
    def stop_recording(self) -> Optional[RecordingSession]:
        """停止录制"""
        if self.state == RecordingState.STOPPED:
            logger.warning("录制未在进行中")
            return None
        
        self.state = RecordingState.STOPPED
        self._stop_listeners()
        
        # 最终截图
        if self.record_screenshots:
            self._take_screenshot("session_end")
        
        # 创建录制会话
        duration = time.time() - self.start_time if self.start_time else 0
        session = RecordingSession(
            name=self.session_name,
            description=f"录制会话，包含 {len(self.events)} 个事件",
            created_at=datetime.now(),
            duration=duration,
            events=self.events.copy(),
            metadata={
                "event_count": len(self.events),
                "screenshots_enabled": self.record_screenshots
            }
        )
        
        # 保存会话
        self._save_session(session)
        
        logger.info(f"录制完成: {self.session_name}，时长: {duration:.2f}秒，事件数: {len(self.events)}")
        return session
    
    def _start_listeners(self):
        """启动事件监听器"""
        # 鼠标监听器
        self.mouse_listener = mouse.Listener(
            on_click=self._on_mouse_click,
            on_scroll=self._on_mouse_scroll
        )
        self.mouse_listener.start()
        
        # 键盘监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        self.keyboard_listener.start()
    
    def _stop_listeners(self):
        """停止事件监听器"""
        if self.mouse_listener:
            self.mouse_listener.stop()
            self.mouse_listener = None
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            self.keyboard_listener = None
    
    def _record_event(self, event_type: str, data: Dict[str, Any]):
        """记录事件"""
        if self.state != RecordingState.RECORDING:
            return
        
        current_time = time.time()
        timestamp = current_time - self.start_time if self.start_time else 0
        
        # 检查是否需要截图
        screenshot_path = None
        if (self.record_screenshots and 
            current_time - self.last_screenshot_time >= self.screenshot_interval):
            screenshot_path = self._take_screenshot(f"event_{len(self.events)}")
            self.last_screenshot_time = current_time
        
        event = RecordedEvent(
            timestamp=timestamp,
            event_type=event_type,
            data=data,
            screenshot_path=screenshot_path
        )
        
        self.events.append(event)
        
        # 调用回调
        if self.on_event_recorded:
            self.on_event_recorded(event)
        
        logger.debug(f"记录事件: {event_type} at {timestamp:.2f}s")
    
    def _take_screenshot(self, suffix: str = "") -> str:
        """截图"""
        try:
            timestamp = int(time.time() * 1000)
            filename = f"{self.session_name}_{timestamp}_{suffix}.png"
            screenshot_path = self.recordings_dir / self.session_name / "screenshots" / filename
            screenshot_path.parent.mkdir(parents=True, exist_ok=True)
            
            screenshot = pyautogui.screenshot()
            screenshot.save(screenshot_path)
            
            return str(screenshot_path)
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return ""
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if pressed:  # 只记录按下事件
            self._record_event("mouse_click", {
                "x": x,
                "y": y,
                "button": button.name,
                "action": "press"
            })
    
    def _on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚动事件"""
        self._record_event("mouse_scroll", {
            "x": x,
            "y": y,
            "dx": dx,
            "dy": dy
        })
    
    def _on_key_press(self, key):
        """按键按下事件"""
        try:
            key_name = key.char if hasattr(key, 'char') and key.char else str(key)
            self._record_event("key_press", {
                "key": key_name,
                "action": "press"
            })
        except Exception as e:
            logger.debug(f"按键记录失败: {e}")
    
    def _on_key_release(self, key):
        """按键释放事件"""
        # 通常不需要记录释放事件，除非是特殊按键
        pass
    
    def _save_session(self, session: RecordingSession):
        """保存录制会话"""
        try:
            session_dir = self.recordings_dir / session.name
            session_dir.mkdir(exist_ok=True)
            
            # 保存会话数据
            session_file = session_dir / "session.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                # 转换为可序列化的格式
                session_data = {
                    "name": session.name,
                    "description": session.description,
                    "created_at": session.created_at.isoformat(),
                    "duration": session.duration,
                    "events": [asdict(event) for event in session.events],
                    "metadata": session.metadata
                }
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"会话已保存: {session_file}")
            
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

class ActionPlayer:
    """操作回放器"""
    
    def __init__(self):
        self.executor = AutomationExecutor()
        self.screen_parser = ScreenParser()
        self.recordings_dir = Path("recordings")
        
        # 回放配置
        self.playback_speed = 1.0  # 回放速度倍率
        self.verify_elements = True  # 是否验证元素存在
        
        # 回放状态
        self.is_playing = False
        self.current_session: Optional[RecordingSession] = None
        
        # 回调函数
        self.on_event_played: Optional[Callable] = None
        self.on_playback_complete: Optional[Callable] = None
    
    def load_session(self, session_name: str) -> Optional[RecordingSession]:
        """加载录制会话"""
        try:
            session_file = self.recordings_dir / session_name / "session.json"
            if not session_file.exists():
                logger.error(f"会话文件不存在: {session_file}")
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            # 重构事件对象
            events = []
            for event_data in session_data["events"]:
                event = RecordedEvent(**event_data)
                events.append(event)
            
            session = RecordingSession(
                name=session_data["name"],
                description=session_data["description"],
                created_at=datetime.fromisoformat(session_data["created_at"]),
                duration=session_data["duration"],
                events=events,
                metadata=session_data["metadata"]
            )
            
            logger.info(f"会话已加载: {session_name}")
            return session
            
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
            return None
    
    def play_session(self, session: RecordingSession, start_delay: float = 2.0):
        """回放会话"""
        if self.is_playing:
            logger.warning("回放已在进行中")
            return
        
        self.is_playing = True
        self.current_session = session
        
        logger.info(f"开始回放会话: {session.name}")
        logger.info(f"等待 {start_delay} 秒后开始...")
        
        # 在新线程中执行回放
        threading.Thread(
            target=self._play_session_thread,
            args=(session, start_delay),
            daemon=True
        ).start()
    
    def _play_session_thread(self, session: RecordingSession, start_delay: float):
        """回放线程"""
        try:
            time.sleep(start_delay)
            
            last_timestamp = 0
            for i, event in enumerate(session.events):
                if not self.is_playing:
                    break
                
                # 计算等待时间
                wait_time = (event.timestamp - last_timestamp) / self.playback_speed
                if wait_time > 0:
                    time.sleep(wait_time)
                
                # 执行事件
                self._play_event(event)
                
                # 调用回调
                if self.on_event_played:
                    self.on_event_played(event, i + 1, len(session.events))
                
                last_timestamp = event.timestamp
            
            self.is_playing = False
            logger.info("回放完成")
            
            if self.on_playback_complete:
                self.on_playback_complete(session)
                
        except Exception as e:
            self.is_playing = False
            logger.error(f"回放失败: {e}")
    
    def _play_event(self, event: RecordedEvent):
        """执行单个事件"""
        try:
            if event.event_type == "mouse_click":
                self._play_mouse_click(event.data)
            elif event.event_type == "mouse_scroll":
                self._play_mouse_scroll(event.data)
            elif event.event_type == "key_press":
                self._play_key_press(event.data)
            else:
                logger.debug(f"跳过未知事件类型: {event.event_type}")
                
        except Exception as e:
            logger.error(f"事件执行失败: {e}")
    
    def _play_mouse_click(self, data: Dict[str, Any]):
        """执行鼠标点击"""
        x, y = data["x"], data["y"]
        button = data["button"]
        
        if button == "left":
            pyautogui.click(x, y)
        elif button == "right":
            pyautogui.rightClick(x, y)
        elif button == "middle":
            pyautogui.middleClick(x, y)
    
    def _play_mouse_scroll(self, data: Dict[str, Any]):
        """执行鼠标滚动"""
        x, y = data["x"], data["y"]
        dy = data["dy"]
        
        pyautogui.scroll(int(dy * 3), x=x, y=y)
    
    def _play_key_press(self, data: Dict[str, Any]):
        """执行按键"""
        key = data["key"]
        
        # 处理特殊按键
        if key.startswith("Key."):
            key = key.replace("Key.", "")
        
        try:
            pyautogui.press(key)
        except:
            # 如果是字符，直接输入
            if len(key) == 1:
                pyautogui.typewrite(key)
    
    def stop_playback(self):
        """停止回放"""
        self.is_playing = False
        logger.info("回放已停止")
    
    def list_sessions(self) -> List[str]:
        """列出所有会话"""
        sessions = []
        if self.recordings_dir.exists():
            for session_dir in self.recordings_dir.iterdir():
                if session_dir.is_dir() and (session_dir / "session.json").exists():
                    sessions.append(session_dir.name)
        return sessions

# 使用示例
if __name__ == "__main__":
    # 创建录制器和回放器
    recorder = ActionRecorder()
    player = ActionPlayer()
    
    # 测试录制
    print("开始录制测试...")
    recorder.start_recording("test_session")
    
    # 模拟一些操作
    time.sleep(2)
    pyautogui.click(100, 100)
    time.sleep(1)
    pyautogui.typewrite("hello")
    time.sleep(1)
    
    # 停止录制
    session = recorder.stop_recording()
    
    if session:
        print(f"录制完成，事件数: {len(session.events)}")
        
        # 测试回放
        print("开始回放测试...")
        player.play_session(session)
    
    # 列出所有会话
    sessions = player.list_sessions()
    print(f"可用会话: {sessions}")
