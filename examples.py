"""
使用示例 - 展示OmniParser自动化工具的各种用法
"""
import time
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from main import OmniParserAutomation
from rich.console import Console
from rich.panel import Panel

console = Console()

def example_basic_operations():
    """基础操作示例"""
    console.print(Panel("基础操作示例", style="blue"))
    
    automation = OmniParserAutomation()
    
    # 基础点击操作
    examples = [
        "点击开始按钮",
        "双击桌面图标",
        "右键点击文件",
        "点击坐标(100, 200)"
    ]
    
    console.print("🖱️ 鼠标操作示例:")
    for example in examples:
        console.print(f"  • {example}")
        # automation.execute_instruction(example, preview=True)
    
    console.print("\n✅ 基础操作示例完成")

def example_text_input():
    """文本输入示例"""
    console.print(Panel("文本输入示例", style="green"))
    
    automation = OmniParserAutomation()
    
    # 文本输入操作
    examples = [
        "在搜索框中输入'Python编程'",
        "在用户名输入框中输入'admin'",
        "在密码框中输入'123456'",
        "输入邮箱地址'<EMAIL>'"
    ]
    
    console.print("⌨️ 文本输入示例:")
    for example in examples:
        console.print(f"  • {example}")
        # automation.execute_instruction(example, preview=True)
    
    console.print("\n✅ 文本输入示例完成")

def example_navigation():
    """导航操作示例"""
    console.print(Panel("导航操作示例", style="yellow"))
    
    automation = OmniParserAutomation()
    
    # 导航操作
    examples = [
        "向下滚动",
        "向上滚动3次",
        "向右滚动",
        "滚动到页面底部"
    ]
    
    console.print("🧭 导航操作示例:")
    for example in examples:
        console.print(f"  • {example}")
        # automation.execute_instruction(example, preview=True)
    
    console.print("\n✅ 导航操作示例完成")

def example_complex_workflow():
    """复杂工作流示例"""
    console.print(Panel("复杂工作流示例", style="magenta"))
    
    automation = OmniParserAutomation()
    
    # 模拟登录流程
    login_steps = [
        "点击登录按钮",
        "在用户名输入框中输入'admin'",
        "在密码输入框中输入'password123'",
        "点击确定按钮",
        "等待2秒"
    ]
    
    console.print("🔐 登录流程示例:")
    for i, step in enumerate(login_steps, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
        # time.sleep(0.5)  # 步骤间延迟
    
    # 模拟文件操作流程
    file_operations = [
        "右键点击桌面",
        "点击新建菜单",
        "点击文本文档",
        "在文件名输入框中输入'测试文档.txt'",
        "按回车键确认"
    ]
    
    console.print("\n📁 文件操作示例:")
    for i, step in enumerate(file_operations, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
    
    console.print("\n✅ 复杂工作流示例完成")

def example_web_automation():
    """网页自动化示例"""
    console.print(Panel("网页自动化示例", style="cyan"))
    
    automation = OmniParserAutomation()
    
    # 网页操作流程
    web_steps = [
        "点击地址栏",
        "输入网址'https://www.google.com'",
        "按回车键",
        "等待3秒",
        "点击搜索框",
        "输入搜索内容'Python自动化'",
        "点击搜索按钮",
        "等待2秒",
        "点击第一个搜索结果"
    ]
    
    console.print("🌐 网页自动化示例:")
    for i, step in enumerate(web_steps, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
    
    console.print("\n✅ 网页自动化示例完成")

def example_application_automation():
    """应用程序自动化示例"""
    console.print(Panel("应用程序自动化示例", style="red"))
    
    automation = OmniParserAutomation()
    
    # 记事本操作示例
    notepad_steps = [
        "按Win+R键",
        "输入'notepad'",
        "按回车键",
        "等待1秒",
        "在文本区域输入'这是一个自动化测试'",
        "按Ctrl+S保存",
        "在文件名输入框中输入'自动化测试.txt'",
        "点击保存按钮"
    ]
    
    console.print("📝 记事本操作示例:")
    for i, step in enumerate(notepad_steps, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
    
    # 计算器操作示例
    calculator_steps = [
        "按Win+R键",
        "输入'calc'",
        "按回车键",
        "等待1秒",
        "点击数字1",
        "点击加号",
        "点击数字2",
        "点击等号",
        "截图保存结果"
    ]
    
    console.print("\n🧮 计算器操作示例:")
    for i, step in enumerate(calculator_steps, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
    
    console.print("\n✅ 应用程序自动化示例完成")

def example_error_handling():
    """错误处理示例"""
    console.print(Panel("错误处理示例", style="bright_red"))
    
    automation = OmniParserAutomation()
    
    # 错误情况示例
    error_cases = [
        "点击不存在的按钮",  # 找不到元素
        "在不存在的输入框中输入文本",  # 目标不存在
        "执行无效操作",  # 无法解析的指令
        "点击坐标(-100, -100)"  # 无效坐标
    ]
    
    console.print("❌ 错误处理示例:")
    for case in error_cases:
        console.print(f"  • {case}")
        try:
            # automation.execute_instruction(case, preview=True)
            console.print(f"    ✅ 错误已正确处理")
        except Exception as e:
            console.print(f"    ❌ 未处理的错误: {e}")
    
    console.print("\n✅ 错误处理示例完成")

def example_batch_operations():
    """批量操作示例"""
    console.print(Panel("批量操作示例", style="bright_green"))
    
    automation = OmniParserAutomation()
    
    # 批量文件重命名示例
    batch_rename = [
        "选择第一个文件",
        "按F2键重命名",
        "输入新文件名'文档_001'",
        "按回车确认",
        "选择下一个文件",
        "按F2键重命名",
        "输入新文件名'文档_002'",
        "按回车确认"
    ]
    
    console.print("📂 批量文件重命名示例:")
    for i, step in enumerate(batch_rename, 1):
        console.print(f"  {i}. {step}")
        # automation.execute_instruction(step, preview=True)
    
    console.print("\n✅ 批量操作示例完成")

def run_all_examples():
    """运行所有示例"""
    console.print(Panel(
        "OmniParser 自动化工具使用示例\n"
        "以下示例展示了各种自动化操作的用法",
        title="使用示例",
        style="bold blue"
    ))
    
    examples = [
        ("基础操作", example_basic_operations),
        ("文本输入", example_text_input),
        ("导航操作", example_navigation),
        ("复杂工作流", example_complex_workflow),
        ("网页自动化", example_web_automation),
        ("应用程序自动化", example_application_automation),
        ("错误处理", example_error_handling),
        ("批量操作", example_batch_operations)
    ]
    
    for name, func in examples:
        console.print(f"\n{'='*50}")
        console.print(f"运行示例: {name}")
        console.print('='*50)
        
        try:
            func()
        except Exception as e:
            console.print(f"❌ 示例 '{name}' 运行失败: {e}", style="red")
        
        console.print(f"\n示例 '{name}' 完成")
        time.sleep(1)  # 示例间延迟
    
    console.print(Panel(
        "所有示例运行完成！\n"
        "您可以参考这些示例来使用 OmniParser 自动化工具。",
        title="完成",
        style="bold green"
    ))

def interactive_demo():
    """交互式演示"""
    console.print(Panel(
        "交互式演示模式\n"
        "您可以输入指令来测试自动化功能",
        title="交互式演示",
        style="bold cyan"
    ))
    
    automation = OmniParserAutomation()
    
    # 提供一些建议指令
    suggestions = [
        "点击开始按钮",
        "在搜索框中输入'测试'",
        "向下滚动3次",
        "等待2秒",
        "截图",
        "双击桌面图标"
    ]
    
    console.print("💡 建议的指令:")
    for suggestion in suggestions:
        console.print(f"  • {suggestion}")
    
    console.print("\n输入 'quit' 退出演示")
    
    while True:
        try:
            instruction = input("\n请输入指令: ").strip()
            
            if instruction.lower() in ['quit', 'exit', '退出']:
                console.print("👋 演示结束！", style="green")
                break
            
            if instruction:
                console.print(f"执行指令: {instruction}")
                # 预览模式，不执行实际操作
                automation.execute_instruction(instruction, preview=True)
            
        except KeyboardInterrupt:
            console.print("\n👋 演示结束！", style="green")
            break
        except Exception as e:
            console.print(f"❌ 错误: {e}", style="red")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            interactive_demo()
        elif sys.argv[1] == "all":
            run_all_examples()
        else:
            console.print("用法: python examples.py [demo|all]")
    else:
        # 默认运行所有示例
        run_all_examples()
