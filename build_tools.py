"""
构建和打包工具
"""
import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
from typing import List, Dict, Any
import json

class ProjectBuilder:
    """项目构建器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        
        # 项目信息
        self.project_info = {
            "name": "OmniParser自动化工具",
            "version": "1.0.0",
            "description": "基于OmniParser的智能电脑自动化操作工具",
            "author": "AI Assistant",
            "license": "MIT"
        }
    
    def clean_build(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        print("✅ 构建目录清理完成")
    
    def create_executable(self):
        """创建可执行文件"""
        print("创建可执行文件...")
        
        try:
            # 检查PyInstaller是否安装
            subprocess.run([sys.executable, "-c", "import PyInstaller"], 
                          check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print("安装PyInstaller...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                          check=True)
        
        # PyInstaller配置
        spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=['{self.project_root}'],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('weights', 'weights'),
        ('screenshots', 'screenshots'),
        ('logs', 'logs'),
    ],
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.ttk',
        'customtkinter',
        'pyautogui',
        'pynput',
        'rich',
        'click',
        'openai',
        'anthropic',
        'torch',
        'transformers',
        'ultralytics',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OmniParser自动化工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if Path('icon.ico').exists() else None,
)
'''
        
        # 写入spec文件
        spec_file = self.build_dir / "omniparser.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 执行PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--onefile",
            str(spec_file)
        ]
        
        result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 可执行文件创建成功")
            return True
        else:
            print(f"❌ 可执行文件创建失败: {result.stderr}")
            return False
    
    def create_installer(self):
        """创建安装包"""
        print("创建安装包...")
        
        # 创建安装脚本
        installer_script = f'''
@echo off
echo 安装 {self.project_info["name"]} v{self.project_info["version"]}
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 创建安装目录
set INSTALL_DIR=%PROGRAMFILES%\\OmniParser
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM 复制文件
echo 复制程序文件...
xcopy /E /I /Y . "%INSTALL_DIR%"

REM 安装依赖
echo 安装依赖包...
cd /d "%INSTALL_DIR%"
python -m pip install -r requirements.txt

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\\Desktop
echo @echo off > "%DESKTOP%\\OmniParser自动化工具.bat"
echo cd /d "%INSTALL_DIR%" >> "%DESKTOP%\\OmniParser自动化工具.bat"
echo python run.py >> "%DESKTOP%\\OmniParser自动化工具.bat"

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
pause
'''
        
        installer_file = self.dist_dir / "install.bat"
        with open(installer_file, 'w', encoding='gbk') as f:
            f.write(installer_script)
        
        print("✅ 安装脚本创建完成")
    
    def create_portable_package(self):
        """创建便携版包"""
        print("创建便携版包...")
        
        portable_dir = self.dist_dir / "OmniParser_Portable"
        portable_dir.mkdir(exist_ok=True)
        
        # 需要包含的文件和目录
        include_files = [
            "main.py", "gui.py", "run.py", "config.py",
            "screen_parser.py", "instruction_parser.py", 
            "automation_executor.py", "omniparser_integration.py",
            "logger_config.py", "error_handler.py", "recorder.py",
            "performance_optimizer.py", "automation_templates.py",
            "setup.py", "test_automation.py", "examples.py",
            "requirements.txt", "README.md", "PROJECT_SUMMARY.md",
            ".env"
        ]
        
        include_dirs = [
            "weights", "screenshots", "logs", "recordings"
        ]
        
        # 复制文件
        for file_name in include_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                shutil.copy2(src_file, portable_dir)
        
        # 复制目录
        for dir_name in include_dirs:
            src_dir = self.project_root / dir_name
            dst_dir = portable_dir / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, dst_dir, dirs_exist_ok=True)
            else:
                dst_dir.mkdir(exist_ok=True)
        
        # 创建启动脚本
        start_script = '''@echo off
echo 启动 OmniParser 自动化工具
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖
echo 检查依赖包...
python -c "import pyautogui, rich, click" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    python -m pip install -r requirements.txt
)

REM 启动程序
echo 启动程序...
python run.py

pause
'''
        
        start_file = portable_dir / "启动.bat"
        with open(start_file, 'w', encoding='gbk') as f:
            f.write(start_script)
        
        # 创建说明文件
        readme_content = f'''
# {self.project_info["name"]} 便携版

版本: {self.project_info["version"]}
描述: {self.project_info["description"]}

## 使用方法

1. 确保已安装 Python 3.8 或更高版本
2. 双击 "启动.bat" 运行程序
3. 首次运行会自动安装依赖包

## 文件说明

- 启动.bat: 程序启动脚本
- main.py: 主程序文件
- gui.py: 图形界面
- run.py: 快速启动脚本
- requirements.txt: 依赖包列表
- README.md: 详细说明文档

## 注意事项

- 请确保网络连接正常（用于下载模型和API调用）
- 某些操作可能需要管理员权限
- 建议在使用前阅读 README.md 文档

## 技术支持

如有问题，请查看日志文件或提交 GitHub Issue。
'''
        
        readme_file = portable_dir / "使用说明.txt"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ 便携版包创建完成")
        return portable_dir
    
    def create_zip_package(self):
        """创建ZIP压缩包"""
        print("创建ZIP压缩包...")
        
        # 创建便携版
        portable_dir = self.create_portable_package()
        
        # 压缩为ZIP
        zip_file = self.dist_dir / f"OmniParser_v{self.project_info['version']}.zip"
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path in portable_dir.rglob('*'):
                if file_path.is_file():
                    arc_name = file_path.relative_to(portable_dir.parent)
                    zf.write(file_path, arc_name)
        
        print(f"✅ ZIP包创建完成: {zip_file}")
        return zip_file
    
    def create_documentation(self):
        """创建文档包"""
        print("创建文档包...")
        
        docs_dir = self.dist_dir / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # 复制文档文件
        doc_files = ["README.md", "PROJECT_SUMMARY.md"]
        for doc_file in doc_files:
            src_file = self.project_root / doc_file
            if src_file.exists():
                shutil.copy2(src_file, docs_dir)
        
        # 生成API文档
        api_doc = docs_dir / "API文档.md"
        with open(api_doc, 'w', encoding='utf-8') as f:
            f.write(self._generate_api_documentation())
        
        print("✅ 文档包创建完成")
    
    def _generate_api_documentation(self) -> str:
        """生成API文档"""
        return f'''
# {self.project_info["name"]} API 文档

## 主要模块

### main.py
主程序入口，提供命令行界面。

### gui.py  
图形用户界面模块。

### screen_parser.py
屏幕解析模块，集成OmniParser进行UI元素识别。

### instruction_parser.py
指令解析模块，将自然语言转换为操作指令。

### automation_executor.py
自动化执行模块，执行具体的操作指令。

### automation_templates.py
自动化模板模块，提供常见场景的预设模板。

### recorder.py
操作录制回放模块。

### performance_optimizer.py
性能优化模块。

### error_handler.py
错误处理模块。

### logger_config.py
日志配置模块。

## 使用示例

```python
from main import OmniParserAutomation

# 创建自动化实例
automation = OmniParserAutomation()

# 执行指令
success = automation.execute_instruction("点击开始按钮")
```

更多详细信息请参考 README.md 文件。
'''
    
    def build_all(self):
        """构建所有包"""
        print(f"开始构建 {self.project_info['name']} v{self.project_info['version']}")
        print("=" * 50)
        
        # 清理构建目录
        self.clean_build()
        
        # 创建各种包
        self.create_installer()
        self.create_zip_package()
        self.create_documentation()
        
        # 可选：创建可执行文件（需要PyInstaller）
        try:
            self.create_executable()
        except Exception as e:
            print(f"⚠️ 可执行文件创建跳过: {e}")
        
        print("\n" + "=" * 50)
        print("构建完成！")
        print(f"输出目录: {self.dist_dir}")
        
        # 列出生成的文件
        print("\n生成的文件:")
        for file_path in self.dist_dir.rglob('*'):
            if file_path.is_file():
                size = file_path.stat().st_size
                size_str = self._format_size(size)
                print(f"  {file_path.name} ({size_str})")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"

def main():
    """主函数"""
    builder = ProjectBuilder()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "clean":
            builder.clean_build()
        elif command == "exe":
            builder.create_executable()
        elif command == "installer":
            builder.create_installer()
        elif command == "portable":
            builder.create_portable_package()
        elif command == "zip":
            builder.create_zip_package()
        elif command == "docs":
            builder.create_documentation()
        elif command == "all":
            builder.build_all()
        else:
            print("未知命令。可用命令: clean, exe, installer, portable, zip, docs, all")
    else:
        builder.build_all()

if __name__ == "__main__":
    main()
