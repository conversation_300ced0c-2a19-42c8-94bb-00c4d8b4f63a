"""
主程序 - OmniParser自动化操作工具
"""
import os
import sys
import time
import logging
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from dotenv import load_dotenv

from config import create_directories, LOGGING_CONFIG
from screen_parser import ScreenParser
from instruction_parser import InstructionParser
from automation_executor import AutomationExecutor

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["log_file"], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建控制台对象
console = Console()

class OmniParserAutomation:
    """OmniParser自动化主类"""
    
    def __init__(self):
        self.screen_parser = ScreenParser()
        self.instruction_parser = InstructionParser()
        self.executor = AutomationExecutor()
        self.initialized = False
    
    def initialize(self):
        """初始化系统"""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # 创建目录
                task1 = progress.add_task("创建项目目录...", total=None)
                create_directories()
                progress.update(task1, completed=True)
                
                # 加载模型
                task2 = progress.add_task("加载OmniParser模型...", total=None)
                self.screen_parser.load_models()
                progress.update(task2, completed=True)
                
                self.initialized = True
                console.print("✅ 系统初始化完成", style="green")
                
        except Exception as e:
            console.print(f"❌ 初始化失败: {e}", style="red")
            logger.error(f"初始化失败: {e}")
            return False
        
        return True
    
    def execute_instruction(self, instruction: str, preview: bool = False, 
                          save_screenshot: bool = True) -> bool:
        """执行自然语言指令"""
        try:
            if not self.initialized:
                console.print("⚠️ 系统未初始化，正在初始化...", style="yellow")
                if not self.initialize():
                    return False
            
            console.print(Panel(f"指令: {instruction}", title="执行任务", style="blue"))
            
            # 截图
            console.print("📸 正在截取屏幕...")
            screenshot_path = self.screen_parser.take_screenshot()
            
            # 解析屏幕
            console.print("🔍 正在解析屏幕元素...")
            ui_elements = self.screen_parser.parse_screenshot(screenshot_path)
            
            if not ui_elements:
                console.print("❌ 未检测到可交互元素", style="red")
                return False
            
            # 显示检测到的元素
            self._display_ui_elements(ui_elements)
            
            # 解析指令
            console.print("🧠 正在解析指令...")
            actions = self.instruction_parser.parse_instruction(instruction, ui_elements)
            
            if not actions:
                console.print("❌ 无法解析指令", style="red")
                return False
            
            # 显示解析的操作
            self._display_actions(actions)
            
            # 预览模式
            if preview:
                console.print("👀 预览模式，不执行实际操作", style="yellow")
                return True
            
            # 确认执行
            if not click.confirm("是否执行以上操作？"):
                console.print("❌ 用户取消操作", style="yellow")
                return False
            
            # 执行操作
            console.print("🚀 正在执行操作...")
            success = self.executor.execute_actions(actions, ui_elements)
            
            if success:
                console.print("✅ 操作执行成功", style="green")
                
                # 保存标注截图
                if save_screenshot:
                    annotated_path = self.screen_parser.annotate_screenshot(
                        screenshot_path, ui_elements
                    )
                    console.print(f"📋 标注截图已保存: {annotated_path}")
                
            else:
                console.print("❌ 操作执行失败", style="red")
            
            return success
            
        except Exception as e:
            console.print(f"❌ 执行失败: {e}", style="red")
            logger.error(f"执行失败: {e}")
            return False
    
    def _display_ui_elements(self, ui_elements):
        """显示UI元素表格"""
        table = Table(title="检测到的UI元素")
        table.add_column("序号", style="cyan", no_wrap=True)
        table.add_column("描述", style="magenta")
        table.add_column("位置", style="green")
        table.add_column("可交互", style="yellow")
        
        for i, element in enumerate(ui_elements):
            table.add_row(
                str(i + 1),
                element.description,
                f"({element.center_x}, {element.center_y})",
                "✅" if element.is_interactable else "❌"
            )
        
        console.print(table)
    
    def _display_actions(self, actions):
        """显示操作列表"""
        table = Table(title="解析的操作")
        table.add_column("序号", style="cyan", no_wrap=True)
        table.add_column("操作类型", style="magenta")
        table.add_column("目标", style="green")
        table.add_column("参数", style="yellow")
        
        for i, action in enumerate(actions):
            params = []
            if action.text:
                params.append(f"文本: {action.text}")
            if action.coordinates:
                params.append(f"坐标: {action.coordinates}")
            if action.direction:
                params.append(f"方向: {action.direction}")
            if action.distance:
                params.append(f"距离: {action.distance}")
            if action.duration:
                params.append(f"时长: {action.duration}s")
            
            table.add_row(
                str(i + 1),
                action.action_type.value,
                action.target or "-",
                ", ".join(params) or "-"
            )
        
        console.print(table)

# CLI命令定义
@click.group()
@click.version_option(version="1.0.0", prog_name="OmniParser自动化工具")
def cli():
    """OmniParser自动化操作工具
    
    使用自然语言控制电脑操作
    """
    pass

@cli.command()
@click.argument('instruction')
@click.option('--preview', '-p', is_flag=True, help='预览模式，不执行实际操作')
@click.option('--no-screenshot', is_flag=True, help='不保存标注截图')
def run(instruction: str, preview: bool, no_screenshot: bool):
    """执行自然语言指令
    
    INSTRUCTION: 要执行的自然语言指令
    
    示例:
    python main.py run "点击开始按钮"
    python main.py run "在搜索框中输入'hello world'" --preview
    """
    automation = OmniParserAutomation()
    success = automation.execute_instruction(
        instruction, 
        preview=preview, 
        save_screenshot=not no_screenshot
    )
    
    sys.exit(0 if success else 1)

@cli.command()
def interactive():
    """交互模式"""
    automation = OmniParserAutomation()
    
    console.print(Panel(
        "欢迎使用OmniParser自动化工具交互模式\n"
        "输入自然语言指令来控制电脑操作\n"
        "输入 'quit' 或 'exit' 退出",
        title="交互模式",
        style="blue"
    ))
    
    while True:
        try:
            instruction = click.prompt("\n请输入指令", type=str)
            
            if instruction.lower() in ['quit', 'exit', '退出']:
                console.print("👋 再见！", style="green")
                break
            
            if instruction.strip():
                automation.execute_instruction(instruction)
            
        except KeyboardInterrupt:
            console.print("\n👋 再见！", style="green")
            break
        except Exception as e:
            console.print(f"❌ 错误: {e}", style="red")

@cli.command()
def setup():
    """设置和初始化项目"""
    console.print("🚀 开始设置项目...")
    
    try:
        # 运行setup.py
        import subprocess
        result = subprocess.run([sys.executable, "setup.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("✅ 项目设置完成", style="green")
            if result.stdout:
                console.print(result.stdout)
        else:
            console.print("❌ 项目设置失败", style="red")
            if result.stderr:
                console.print(result.stderr)
            
    except Exception as e:
        console.print(f"❌ 设置失败: {e}", style="red")

@cli.command()
def test():
    """测试系统功能"""
    automation = OmniParserAutomation()
    
    console.print("🧪 开始系统测试...")
    
    # 测试初始化
    if automation.initialize():
        console.print("✅ 初始化测试通过", style="green")
    else:
        console.print("❌ 初始化测试失败", style="red")
        return
    
    # 测试截图
    try:
        screenshot_path = automation.screen_parser.take_screenshot()
        console.print(f"✅ 截图测试通过: {screenshot_path}", style="green")
    except Exception as e:
        console.print(f"❌ 截图测试失败: {e}", style="red")
        return
    
    console.print("✅ 所有测试通过", style="green")

if __name__ == "__main__":
    cli()
