"""
图形用户界面 - 提供简单易用的GUI界面
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import time
from pathlib import Path

from main import OmniParserAutomation
from rich.console import Console

class AutomationGUI:
    """自动化工具GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("OmniParser 自动化工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 创建自动化对象
        self.automation = OmniParserAutomation()
        self.console = Console()
        
        # 创建消息队列用于线程间通信
        self.message_queue = queue.Queue()
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 启动消息处理
        self.process_queue()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="OmniParser 自动化工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 指令输入区域
        ttk.Label(main_frame, text="指令输入:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        self.instruction_entry = ttk.Entry(input_frame, font=('Arial', 11))
        self.instruction_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        self.instruction_entry.bind('<Return>', self.on_execute_click)
        
        # 按钮框架
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=0, column=1)
        
        self.execute_btn = ttk.Button(button_frame, text="执行", command=self.on_execute_click)
        self.execute_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.preview_btn = ttk.Button(button_frame, text="预览", command=self.on_preview_click)
        self.preview_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="清空", command=self.on_clear_click)
        self.clear_btn.grid(row=0, column=2)
        
        # 输出区域
        ttk.Label(main_frame, text="执行日志:", style='Heading.TLabel').grid(row=3, column=0, sticky=tk.W, pady=(10, 5))
        
        # 创建带滚动条的文本框
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(
            text_frame, 
            height=15, 
            font=('Consolas', 10),
            wrap=tk.WORD
        )
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, padx=(0, 10))
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=1, sticky=tk.W)
        
        # 功能按钮
        func_frame = ttk.Frame(status_frame)
        func_frame.grid(row=0, column=2)
        
        ttk.Button(func_frame, text="截图", command=self.on_screenshot_click).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(func_frame, text="测试", command=self.on_test_click).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(func_frame, text="帮助", command=self.on_help_click).grid(row=0, column=2)
        
        # 预设指令
        self.create_preset_commands()
        
        # 初始化状态
        self.log_message("界面初始化完成")
        self.log_message("请输入自然语言指令，例如：'点击开始按钮'")
    
    def create_preset_commands(self):
        """创建预设指令按钮"""
        preset_frame = ttk.LabelFrame(self.root, text="常用指令", padding="5")
        preset_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))
        
        presets = [
            "点击开始按钮",
            "截图",
            "向下滚动",
            "等待2秒",
            "在搜索框中输入'测试'",
            "双击桌面图标"
        ]
        
        for i, preset in enumerate(presets):
            btn = ttk.Button(
                preset_frame, 
                text=preset, 
                command=lambda p=preset: self.set_instruction(p)
            )
            btn.grid(row=i//3, column=i%3, padx=5, pady=2, sticky=tk.W)
    
    def set_instruction(self, instruction):
        """设置指令到输入框"""
        self.instruction_entry.delete(0, tk.END)
        self.instruction_entry.insert(0, instruction)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}\n"
        
        self.output_text.insert(tk.END, formatted_message)
        self.output_text.see(tk.END)
        
        # 根据级别设置颜色
        if level == "ERROR":
            self.output_text.tag_add("error", "end-2l", "end-1l")
            self.output_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.output_text.tag_add("success", "end-2l", "end-1l")
            self.output_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.output_text.tag_add("warning", "end-2l", "end-1l")
            self.output_text.tag_config("warning", foreground="orange")
    
    def update_status(self, status, style=None):
        """更新状态栏"""
        self.status_label.config(text=status)
        if style:
            self.status_label.config(style=f'{style}.TLabel')
    
    def on_execute_click(self, event=None):
        """执行按钮点击事件"""
        instruction = self.instruction_entry.get().strip()
        if not instruction:
            messagebox.showwarning("警告", "请输入指令")
            return
        
        self.execute_instruction(instruction, preview=False)
    
    def on_preview_click(self):
        """预览按钮点击事件"""
        instruction = self.instruction_entry.get().strip()
        if not instruction:
            messagebox.showwarning("警告", "请输入指令")
            return
        
        self.execute_instruction(instruction, preview=True)
    
    def on_clear_click(self):
        """清空按钮点击事件"""
        self.output_text.delete(1.0, tk.END)
        self.log_message("日志已清空")
    
    def on_screenshot_click(self):
        """截图按钮点击事件"""
        self.execute_instruction("截图", preview=False)
    
    def on_test_click(self):
        """测试按钮点击事件"""
        self.log_message("开始系统测试...")
        
        def test_thread():
            try:
                # 测试初始化
                if self.automation.initialize():
                    self.message_queue.put(("log", "初始化测试通过", "SUCCESS"))
                else:
                    self.message_queue.put(("log", "初始化测试失败", "ERROR"))
                    return
                
                # 测试截图
                screenshot_path = self.automation.screen_parser.take_screenshot()
                self.message_queue.put(("log", f"截图测试通过: {screenshot_path}", "SUCCESS"))
                
                self.message_queue.put(("log", "所有测试通过", "SUCCESS"))
                self.message_queue.put(("status", "测试完成", "Success"))
                
            except Exception as e:
                self.message_queue.put(("log", f"测试失败: {e}", "ERROR"))
                self.message_queue.put(("status", "测试失败", "Error"))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def on_help_click(self):
        """帮助按钮点击事件"""
        help_text = """
OmniParser 自动化工具使用说明

支持的指令类型：
• 鼠标操作：点击、双击、右键点击
• 键盘输入：在输入框中输入文本
• 滚动操作：向上/下/左/右滚动
• 等待操作：等待指定时间
• 截图操作：保存屏幕截图

指令示例：
• 点击开始按钮
• 在搜索框中输入'Python'
• 向下滚动3次
• 等待2秒
• 截图

注意事项：
• 确保目标元素在屏幕上可见
• 使用预览模式测试指令
• 某些操作可能需要管理员权限
        """
        
        messagebox.showinfo("帮助", help_text)
    
    def execute_instruction(self, instruction, preview=False):
        """在后台线程中执行指令"""
        self.log_message(f"{'预览' if preview else '执行'}指令: {instruction}")
        self.update_status("执行中...", "Warning")
        
        # 禁用按钮
        self.execute_btn.config(state='disabled')
        self.preview_btn.config(state='disabled')
        
        def execute_thread():
            try:
                success = self.automation.execute_instruction(instruction, preview=preview)
                
                if success:
                    self.message_queue.put(("log", "指令执行成功", "SUCCESS"))
                    self.message_queue.put(("status", "执行成功", "Success"))
                else:
                    self.message_queue.put(("log", "指令执行失败", "ERROR"))
                    self.message_queue.put(("status", "执行失败", "Error"))
                    
            except Exception as e:
                self.message_queue.put(("log", f"执行异常: {e}", "ERROR"))
                self.message_queue.put(("status", "执行异常", "Error"))
            
            finally:
                # 重新启用按钮
                self.message_queue.put(("enable_buttons", None, None))
        
        threading.Thread(target=execute_thread, daemon=True).start()
    
    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                msg_type, message, level = self.message_queue.get_nowait()
                
                if msg_type == "log":
                    self.log_message(message, level)
                elif msg_type == "status":
                    self.update_status(message, level)
                elif msg_type == "enable_buttons":
                    self.execute_btn.config(state='normal')
                    self.preview_btn.config(state='normal')
                    
        except queue.Empty:
            pass
        
        # 每100ms检查一次队列
        self.root.after(100, self.process_queue)
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()

def main():
    """主函数"""
    try:
        app = AutomationGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        messagebox.showerror("错误", f"GUI启动失败: {e}")

if __name__ == "__main__":
    main()
