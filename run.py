#!/usr/bin/env python3
"""
快速启动脚本 - 提供多种启动方式
"""
import sys
import os
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    OmniParser 自动化工具                      ║
║                                                              ║
║  基于微软 OmniParser 的智能电脑自动化操作工具                  ║
║  支持自然语言指令控制电脑操作                                  ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_requirements():
    """检查基本要求"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查必要文件
    required_files = ["main.py", "config.py", "requirements.txt"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 错误: 缺少必要文件 {file}")
            return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def run_setup():
    """运行设置"""
    print("🚀 运行项目设置...")
    try:
        subprocess.run([sys.executable, "setup.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ 项目设置失败")
        return False

def run_cli():
    """运行命令行界面"""
    print("🖥️ 启动命令行界面...")
    try:
        subprocess.run([sys.executable, "main.py", "interactive"])
    except KeyboardInterrupt:
        print("\n👋 再见！")

def run_gui():
    """运行图形界面"""
    print("🖼️ 启动图形界面...")
    try:
        subprocess.run([sys.executable, "gui.py"])
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        print("💡 提示: 尝试安装GUI依赖: pip install customtkinter")

def run_examples():
    """运行示例"""
    print("📚 运行使用示例...")
    try:
        subprocess.run([sys.executable, "examples.py", "all"])
    except KeyboardInterrupt:
        print("\n👋 示例演示结束！")

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    try:
        subprocess.run([sys.executable, "test_automation.py"])
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

def show_menu():
    """显示菜单"""
    menu = """
请选择启动方式:

1. 🖥️  命令行界面 (推荐)
2. 🖼️  图形界面
3. 📚 查看使用示例
4. 🧪 运行测试
5. ⚙️  项目设置
6. 📦 安装依赖
7. ❓ 帮助
8. 🚪 退出

"""
    print(menu)

def show_help():
    """显示帮助信息"""
    help_text = """
OmniParser 自动化工具使用说明

命令行用法:
  python run.py                    # 显示菜单
  python run.py cli                # 直接启动命令行界面
  python run.py gui                # 直接启动图形界面
  python run.py examples           # 运行示例
  python run.py test               # 运行测试
  python run.py setup              # 项目设置
  python run.py install            # 安装依赖

直接使用主程序:
  python main.py run "点击开始按钮"     # 执行单个指令
  python main.py interactive        # 交互模式
  python main.py test               # 系统测试

支持的指令示例:
  • 点击开始按钮
  • 在搜索框中输入'Python'
  • 向下滚动3次
  • 等待2秒
  • 截图

更多信息请查看 README.md 文件。
"""
    print(help_text)

def main():
    """主函数"""
    print_banner()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "cli":
            if check_requirements():
                run_cli()
            return
        elif command == "gui":
            if check_requirements():
                run_gui()
            return
        elif command == "examples":
            if check_requirements():
                run_examples()
            return
        elif command == "test":
            if check_requirements():
                run_tests()
            return
        elif command == "setup":
            run_setup()
            return
        elif command == "install":
            install_dependencies()
            return
        elif command in ["help", "-h", "--help"]:
            show_help()
            return
        else:
            print(f"❌ 未知命令: {command}")
            show_help()
            return
    
    # 交互式菜单
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-8): ").strip()
            
            if choice == "1":
                if check_requirements():
                    run_cli()
                else:
                    print("请先解决上述问题")
            
            elif choice == "2":
                if check_requirements():
                    run_gui()
                else:
                    print("请先解决上述问题")
            
            elif choice == "3":
                if check_requirements():
                    run_examples()
                else:
                    print("请先解决上述问题")
            
            elif choice == "4":
                if check_requirements():
                    run_tests()
                else:
                    print("请先解决上述问题")
            
            elif choice == "5":
                run_setup()
            
            elif choice == "6":
                install_dependencies()
            
            elif choice == "7":
                show_help()
            
            elif choice == "8":
                print("👋 再见！")
                break
            
            else:
                print("❌ 无效选择，请输入 1-8")
            
            print("\n" + "="*60)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
