"""
增强的日志配置模块
"""
import logging
import logging.handlers
import sys
import os
from pathlib import Path
from datetime import datetime
import traceback
import functools
from typing import Any, Callable, Optional

from config import LOGGING_CONFIG

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class ContextFilter(logging.Filter):
    """上下文过滤器，添加额外信息"""
    
    def filter(self, record):
        # 添加进程ID和线程ID
        record.process_id = os.getpid()
        record.thread_id = record.thread
        
        # 添加函数名和行号
        if hasattr(record, 'funcName'):
            record.location = f"{record.filename}:{record.lineno}:{record.funcName}"
        else:
            record.location = f"{record.filename}:{record.lineno}"
        
        return True

class EnhancedLogger:
    """增强的日志记录器"""
    
    def __init__(self, name: str = "omniparser"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志记录器"""
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置日志级别
        level = getattr(logging, LOGGING_CONFIG["level"], logging.INFO)
        self.logger.setLevel(level)
        
        # 创建日志目录
        log_file = Path(LOGGING_CONFIG["log_file"])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器（带轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        
        # 错误处理器（单独记录错误）
        error_file = log_file.parent / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # 设置格式化器
        detailed_format = (
            "%(asctime)s | %(levelname)-8s | %(process_id)d:%(thread_id)d | "
            "%(location)s | %(message)s"
        )
        
        simple_format = "%(asctime)s | %(levelname)-8s | %(message)s"
        
        file_formatter = logging.Formatter(detailed_format)
        console_formatter = ColoredFormatter(simple_format)
        error_formatter = logging.Formatter(detailed_format)
        
        # 应用格式化器
        file_handler.setFormatter(file_formatter)
        console_handler.setFormatter(console_formatter)
        error_handler.setFormatter(error_formatter)
        
        # 添加过滤器
        context_filter = ContextFilter()
        file_handler.addFilter(context_filter)
        error_handler.addFilter(context_filter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(error_handler)
        
        # 防止重复日志
        self.logger.propagate = False
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, exc_info: bool = True, **kwargs):
        """错误日志"""
        self.logger.error(message, exc_info=exc_info, extra=kwargs)
    
    def critical(self, message: str, exc_info: bool = True, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, exc_info=exc_info, extra=kwargs)
    
    def log_function_call(self, func_name: str, args: tuple, kwargs: dict):
        """记录函数调用"""
        args_str = ", ".join(str(arg) for arg in args)
        kwargs_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        params = ", ".join(filter(None, [args_str, kwargs_str]))
        
        self.debug(f"调用函数: {func_name}({params})")
    
    def log_function_result(self, func_name: str, result: Any, duration: float):
        """记录函数结果"""
        self.debug(f"函数 {func_name} 完成，耗时: {duration:.3f}s，结果类型: {type(result).__name__}")
    
    def log_exception(self, exc: Exception, context: str = ""):
        """记录异常详情"""
        exc_type = type(exc).__name__
        exc_msg = str(exc)
        exc_trace = traceback.format_exc()
        
        message = f"异常发生: {exc_type}: {exc_msg}"
        if context:
            message = f"{context} - {message}"
        
        self.error(message)
        self.debug(f"异常堆栈:\n{exc_trace}")

# 全局日志实例
logger = EnhancedLogger()

def log_function_calls(logger_instance: Optional[EnhancedLogger] = None):
    """装饰器：记录函数调用"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger_instance or logger
            func_name = f"{func.__module__}.{func.__name__}"
            
            # 记录函数调用
            log.log_function_call(func_name, args, kwargs)
            
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                log.log_function_result(func_name, result, duration)
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                log.log_exception(e, f"函数 {func_name} 执行失败（耗时: {duration:.3f}s）")
                raise
        
        return wrapper
    return decorator

def handle_exceptions(logger_instance: Optional[EnhancedLogger] = None, 
                     reraise: bool = True):
    """装饰器：处理异常"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger_instance or logger
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log.log_exception(e, f"函数 {func.__name__} 异常")
                if reraise:
                    raise
                return None
        return wrapper
    return decorator

def log_performance(threshold: float = 1.0, 
                   logger_instance: Optional[EnhancedLogger] = None):
    """装饰器：记录性能"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger_instance or logger
            start_time = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                if duration > threshold:
                    log.warning(f"函数 {func.__name__} 执行缓慢: {duration:.3f}s")
                else:
                    log.debug(f"函数 {func.__name__} 执行完成: {duration:.3f}s")
                
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                log.error(f"函数 {func.__name__} 执行失败: {duration:.3f}s")
                raise
        
        return wrapper
    return decorator

# 使用示例
if __name__ == "__main__":
    # 测试日志功能
    logger.info("日志系统初始化完成")
    logger.debug("这是调试信息")
    logger.warning("这是警告信息")
    
    try:
        raise ValueError("测试异常")
    except Exception as e:
        logger.log_exception(e, "测试异常处理")
    
    # 测试装饰器
    @log_function_calls()
    @handle_exceptions()
    @log_performance(threshold=0.1)
    def test_function(x, y=10):
        import time
        time.sleep(0.2)  # 模拟耗时操作
        return x + y
    
    result = test_function(5, y=15)
    logger.info(f"测试函数结果: {result}")
