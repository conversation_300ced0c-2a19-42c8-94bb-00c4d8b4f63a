"""
自动化执行模块 - 执行具体的操作指令
"""
import time
import logging
from typing import List, Optional, Tuple
import random

import pyautogui
import pynput
from pynput import mouse, keyboard

from config import AUTOMATION_CONFIG
from instruction_parser import Action, ActionType
from screen_parser import UIElement, ScreenParser

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
pyautogui.PAUSE = AUTOMATION_CONFIG["click_delay"]

class AutomationExecutor:
    """自动化执行器类"""
    
    def __init__(self):
        self.screen_parser = ScreenParser()
        self.click_delay = AUTOMATION_CONFIG["click_delay"]
        self.type_delay = AUTOMATION_CONFIG["type_delay"]
        self.scroll_speed = AUTOMATION_CONFIG["scroll_speed"]
        self.safety_margin = AUTOMATION_CONFIG["safety_margin"]
        
        # 记录执行历史
        self.execution_history = []
    
    def execute_actions(self, actions: List[Action], ui_elements: List[UIElement]) -> bool:
        """执行操作列表"""
        try:
            logger.info(f"开始执行 {len(actions)} 个操作")
            
            for i, action in enumerate(actions):
                logger.info(f"执行操作 {i+1}/{len(actions)}: {action.action_type.value}")
                
                success = self._execute_single_action(action, ui_elements)
                if not success:
                    logger.error(f"操作 {i+1} 执行失败")
                    return False
                
                # 记录执行历史
                self.execution_history.append({
                    "action": action,
                    "timestamp": time.time(),
                    "success": success
                })
                
                # 操作间延迟
                time.sleep(self.click_delay)
            
            logger.info("所有操作执行完成")
            return True
            
        except Exception as e:
            logger.error(f"操作执行失败: {e}")
            return False
    
    def _execute_single_action(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行单个操作"""
        try:
            if action.action_type == ActionType.CLICK:
                return self._execute_click(action, ui_elements)
            
            elif action.action_type == ActionType.DOUBLE_CLICK:
                return self._execute_double_click(action, ui_elements)
            
            elif action.action_type == ActionType.RIGHT_CLICK:
                return self._execute_right_click(action, ui_elements)
            
            elif action.action_type == ActionType.TYPE:
                return self._execute_type(action, ui_elements)
            
            elif action.action_type == ActionType.SCROLL:
                return self._execute_scroll(action)
            
            elif action.action_type == ActionType.DRAG:
                return self._execute_drag(action, ui_elements)
            
            elif action.action_type == ActionType.WAIT:
                return self._execute_wait(action)
            
            elif action.action_type == ActionType.SCREENSHOT:
                return self._execute_screenshot(action)
            
            else:
                logger.warning(f"不支持的操作类型: {action.action_type}")
                return False
                
        except Exception as e:
            logger.error(f"操作执行异常: {e}")
            return False
    
    def _find_element_coordinates(self, target: str, ui_elements: List[UIElement]) -> Optional[Tuple[int, int]]:
        """查找元素坐标"""
        if not target:
            return None
        
        # 查找匹配的元素
        element = self.screen_parser.find_element_by_description(ui_elements, target)
        if element:
            # 添加随机偏移，模拟人类点击
            offset_x = random.randint(-self.safety_margin, self.safety_margin)
            offset_y = random.randint(-self.safety_margin, self.safety_margin)
            
            x = max(element.x, min(element.center_x + offset_x, element.x + element.width))
            y = max(element.y, min(element.center_y + offset_y, element.y + element.height))
            
            return (x, y)
        
        return None
    
    def _execute_click(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行点击操作"""
        try:
            if action.coordinates:
                x, y = action.coordinates
            else:
                coords = self._find_element_coordinates(action.target, ui_elements)
                if not coords:
                    logger.error(f"找不到目标元素: {action.target}")
                    return False
                x, y = coords
            
            logger.info(f"点击位置: ({x}, {y})")
            pyautogui.click(x, y)
            return True
            
        except Exception as e:
            logger.error(f"点击操作失败: {e}")
            return False
    
    def _execute_double_click(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行双击操作"""
        try:
            if action.coordinates:
                x, y = action.coordinates
            else:
                coords = self._find_element_coordinates(action.target, ui_elements)
                if not coords:
                    logger.error(f"找不到目标元素: {action.target}")
                    return False
                x, y = coords
            
            logger.info(f"双击位置: ({x}, {y})")
            pyautogui.doubleClick(x, y)
            return True
            
        except Exception as e:
            logger.error(f"双击操作失败: {e}")
            return False
    
    def _execute_right_click(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行右键点击操作"""
        try:
            if action.coordinates:
                x, y = action.coordinates
            else:
                coords = self._find_element_coordinates(action.target, ui_elements)
                if not coords:
                    logger.error(f"找不到目标元素: {action.target}")
                    return False
                x, y = coords
            
            logger.info(f"右键点击位置: ({x}, {y})")
            pyautogui.rightClick(x, y)
            return True
            
        except Exception as e:
            logger.error(f"右键点击操作失败: {e}")
            return False
    
    def _execute_type(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行输入操作"""
        try:
            # 如果指定了目标，先点击目标元素
            if action.target:
                coords = self._find_element_coordinates(action.target, ui_elements)
                if coords:
                    x, y = coords
                    logger.info(f"点击输入框: ({x}, {y})")
                    pyautogui.click(x, y)
                    time.sleep(0.2)  # 等待输入框获得焦点
            
            # 输入文本
            if action.text:
                logger.info(f"输入文本: {action.text}")
                pyautogui.typewrite(action.text, interval=self.type_delay)
            
            return True
            
        except Exception as e:
            logger.error(f"输入操作失败: {e}")
            return False
    
    def _execute_scroll(self, action: Action) -> bool:
        """执行滚动操作"""
        try:
            direction = action.direction or "down"
            distance = action.distance or 3
            
            logger.info(f"滚动方向: {direction}, 距离: {distance}")
            
            if direction in ["up", "down"]:
                scroll_amount = distance * self.scroll_speed
                if direction == "up":
                    scroll_amount = -scroll_amount
                pyautogui.scroll(scroll_amount)
            
            elif direction in ["left", "right"]:
                # 水平滚动（如果支持）
                for _ in range(distance):
                    if direction == "left":
                        pyautogui.keyDown('shift')
                        pyautogui.scroll(self.scroll_speed)
                        pyautogui.keyUp('shift')
                    else:
                        pyautogui.keyDown('shift')
                        pyautogui.scroll(-self.scroll_speed)
                        pyautogui.keyUp('shift')
                    time.sleep(0.1)
            
            return True
            
        except Exception as e:
            logger.error(f"滚动操作失败: {e}")
            return False
    
    def _execute_drag(self, action: Action, ui_elements: List[UIElement]) -> bool:
        """执行拖拽操作"""
        try:
            # 这里需要更复杂的实现来处理拖拽
            logger.warning("拖拽操作暂未完全实现")
            return True
            
        except Exception as e:
            logger.error(f"拖拽操作失败: {e}")
            return False
    
    def _execute_wait(self, action: Action) -> bool:
        """执行等待操作"""
        try:
            duration = action.duration or 1.0
            logger.info(f"等待 {duration} 秒")
            time.sleep(duration)
            return True
            
        except Exception as e:
            logger.error(f"等待操作失败: {e}")
            return False
    
    def _execute_screenshot(self, action: Action) -> bool:
        """执行截图操作"""
        try:
            screenshot_path = self.screen_parser.take_screenshot()
            logger.info(f"截图已保存: {screenshot_path}")
            return True
            
        except Exception as e:
            logger.error(f"截图操作失败: {e}")
            return False
    
    def get_execution_history(self) -> List[dict]:
        """获取执行历史"""
        return self.execution_history
    
    def clear_execution_history(self):
        """清空执行历史"""
        self.execution_history.clear()

# 使用示例
if __name__ == "__main__":
    from instruction_parser import InstructionParser
    
    # 创建执行器和解析器
    executor = AutomationExecutor()
    parser = InstructionParser()
    screen_parser = ScreenParser()
    
    # 截图并解析
    screenshot_path = screen_parser.take_screenshot()
    ui_elements = screen_parser.parse_screenshot(screenshot_path)
    
    # 解析指令
    instruction = "点击开始按钮"
    actions = parser.parse_instruction(instruction, ui_elements)
    
    # 执行操作
    if actions:
        success = executor.execute_actions(actions, ui_elements)
        print(f"操作执行{'成功' if success else '失败'}")
    else:
        print("没有可执行的操作")
