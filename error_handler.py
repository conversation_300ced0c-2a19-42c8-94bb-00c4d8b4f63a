"""
错误处理模块
"""
import sys
import traceback
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from logger_config import logger

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误类别"""
    SYSTEM = "system"
    MODEL = "model"
    NETWORK = "network"
    USER_INPUT = "user_input"
    AUTOMATION = "automation"
    CONFIGURATION = "configuration"

@dataclass
class ErrorInfo:
    """错误信息数据类"""
    error_type: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    context: Dict[str, Any]
    traceback: Optional[str] = None
    suggested_action: Optional[str] = None

class AutomationError(Exception):
    """自动化操作异常基类"""
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.AUTOMATION,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, context: Dict = None):
        super().__init__(message)
        self.category = category
        self.severity = severity
        self.context = context or {}

class ModelLoadError(AutomationError):
    """模型加载错误"""
    def __init__(self, message: str, model_name: str = ""):
        super().__init__(message, ErrorCategory.MODEL, ErrorSeverity.HIGH)
        self.context["model_name"] = model_name

class ScreenParseError(AutomationError):
    """屏幕解析错误"""
    def __init__(self, message: str, image_path: str = ""):
        super().__init__(message, ErrorCategory.AUTOMATION, ErrorSeverity.MEDIUM)
        self.context["image_path"] = image_path

class InstructionParseError(AutomationError):
    """指令解析错误"""
    def __init__(self, message: str, instruction: str = ""):
        super().__init__(message, ErrorCategory.USER_INPUT, ErrorSeverity.LOW)
        self.context["instruction"] = instruction

class ExecutionError(AutomationError):
    """执行错误"""
    def __init__(self, message: str, action_type: str = ""):
        super().__init__(message, ErrorCategory.AUTOMATION, ErrorSeverity.MEDIUM)
        self.context["action_type"] = action_type

class NetworkError(AutomationError):
    """网络错误"""
    def __init__(self, message: str, api_name: str = ""):
        super().__init__(message, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM)
        self.context["api_name"] = api_name

class ConfigurationError(AutomationError):
    """配置错误"""
    def __init__(self, message: str, config_key: str = ""):
        super().__init__(message, ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH)
        self.context["config_key"] = config_key

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_history: List[ErrorInfo] = []
        self.error_handlers: Dict[ErrorCategory, List[Callable]] = {
            category: [] for category in ErrorCategory
        }
        self.recovery_strategies: Dict[str, Callable] = {}
        
        # 注册默认处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认错误处理器"""
        # 模型错误处理
        self.register_handler(ErrorCategory.MODEL, self._handle_model_error)
        
        # 网络错误处理
        self.register_handler(ErrorCategory.NETWORK, self._handle_network_error)
        
        # 用户输入错误处理
        self.register_handler(ErrorCategory.USER_INPUT, self._handle_user_input_error)
        
        # 自动化错误处理
        self.register_handler(ErrorCategory.AUTOMATION, self._handle_automation_error)
        
        # 配置错误处理
        self.register_handler(ErrorCategory.CONFIGURATION, self._handle_configuration_error)
    
    def register_handler(self, category: ErrorCategory, handler: Callable):
        """注册错误处理器"""
        self.error_handlers[category].append(handler)
    
    def register_recovery_strategy(self, error_type: str, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[error_type] = strategy
    
    def handle_error(self, error: Exception, context: Dict = None) -> bool:
        """处理错误"""
        try:
            # 创建错误信息
            error_info = self._create_error_info(error, context)
            
            # 记录错误
            self.error_history.append(error_info)
            
            # 记录日志
            self._log_error(error_info)
            
            # 执行处理器
            recovery_success = self._execute_handlers(error_info)
            
            # 尝试恢复
            if not recovery_success:
                recovery_success = self._attempt_recovery(error_info)
            
            return recovery_success
            
        except Exception as e:
            logger.critical(f"错误处理器本身发生错误: {e}")
            return False
    
    def _create_error_info(self, error: Exception, context: Dict = None) -> ErrorInfo:
        """创建错误信息"""
        if isinstance(error, AutomationError):
            category = error.category
            severity = error.severity
            error_context = error.context.copy()
            if context:
                error_context.update(context)
        else:
            category = ErrorCategory.SYSTEM
            severity = ErrorSeverity.MEDIUM
            error_context = context or {}
        
        return ErrorInfo(
            error_type=type(error).__name__,
            message=str(error),
            category=category,
            severity=severity,
            timestamp=datetime.now(),
            context=error_context,
            traceback=traceback.format_exc(),
            suggested_action=self._get_suggested_action(error)
        )
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        message = f"{error_info.error_type}: {error_info.message}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(message, context=error_info.context)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(message, context=error_info.context)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(message, context=error_info.context)
        else:
            logger.info(message, context=error_info.context)
    
    def _execute_handlers(self, error_info: ErrorInfo) -> bool:
        """执行错误处理器"""
        handlers = self.error_handlers.get(error_info.category, [])
        
        for handler in handlers:
            try:
                if handler(error_info):
                    return True
            except Exception as e:
                logger.error(f"错误处理器执行失败: {e}")
        
        return False
    
    def _attempt_recovery(self, error_info: ErrorInfo) -> bool:
        """尝试错误恢复"""
        strategy = self.recovery_strategies.get(error_info.error_type)
        
        if strategy:
            try:
                return strategy(error_info)
            except Exception as e:
                logger.error(f"恢复策略执行失败: {e}")
        
        return False
    
    def _get_suggested_action(self, error: Exception) -> str:
        """获取建议操作"""
        suggestions = {
            "ModelLoadError": "请检查模型文件是否存在，或运行 python setup.py 重新下载模型",
            "NetworkError": "请检查网络连接和API密钥配置",
            "ConfigurationError": "请检查配置文件设置",
            "InstructionParseError": "请检查指令格式，参考使用示例",
            "ScreenParseError": "请确保屏幕内容清晰可见",
            "ExecutionError": "请检查目标元素是否存在且可访问"
        }
        
        return suggestions.get(type(error).__name__, "请查看日志获取更多信息")
    
    # 默认错误处理器
    def _handle_model_error(self, error_info: ErrorInfo) -> bool:
        """处理模型错误"""
        logger.warning("尝试切换到备用模式")
        # 这里可以实现切换到模拟模式的逻辑
        return False
    
    def _handle_network_error(self, error_info: ErrorInfo) -> bool:
        """处理网络错误"""
        logger.warning("网络错误，将使用本地解析")
        # 这里可以实现切换到本地解析的逻辑
        return False
    
    def _handle_user_input_error(self, error_info: ErrorInfo) -> bool:
        """处理用户输入错误"""
        logger.info("用户输入错误，请检查指令格式")
        return False
    
    def _handle_automation_error(self, error_info: ErrorInfo) -> bool:
        """处理自动化错误"""
        logger.warning("自动化操作失败，可能需要重试")
        return False
    
    def _handle_configuration_error(self, error_info: ErrorInfo) -> bool:
        """处理配置错误"""
        logger.error("配置错误，请检查配置文件")
        return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {"total": 0}
        
        stats = {
            "total": len(self.error_history),
            "by_category": {},
            "by_severity": {},
            "by_type": {},
            "recent_errors": []
        }
        
        for error in self.error_history:
            # 按类别统计
            category = error.category.value
            stats["by_category"][category] = stats["by_category"].get(category, 0) + 1
            
            # 按严重程度统计
            severity = error.severity.value
            stats["by_severity"][severity] = stats["by_severity"].get(severity, 0) + 1
            
            # 按类型统计
            error_type = error.error_type
            stats["by_type"][error_type] = stats["by_type"].get(error_type, 0) + 1
        
        # 最近的错误
        stats["recent_errors"] = [
            {
                "type": error.error_type,
                "message": error.message,
                "timestamp": error.timestamp.isoformat(),
                "severity": error.severity.value
            }
            for error in self.error_history[-10:]  # 最近10个错误
        ]
        
        return stats
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        logger.info("错误历史已清空")

# 全局错误处理器实例
error_handler = ErrorHandler()

def safe_execute(func: Callable, *args, **kwargs) -> tuple[bool, Any]:
    """安全执行函数"""
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        success = error_handler.handle_error(e, {"function": func.__name__})
        return success, None

def with_error_handling(reraise: bool = False):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, {"function": func.__name__})
                if reraise:
                    raise
                return None
        return wrapper
    return decorator

# 使用示例
if __name__ == "__main__":
    # 测试错误处理
    try:
        raise ModelLoadError("测试模型加载错误", "test_model")
    except Exception as e:
        error_handler.handle_error(e)
    
    # 查看错误统计
    stats = error_handler.get_error_statistics()
    print("错误统计:", stats)
