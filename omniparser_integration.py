"""
真实OmniParser集成模块
"""
import os
import sys
import logging
import torch
import cv2
import numpy as np
from PIL import Image
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import json

from config import OMNIPARSER_CONFIG
from screen_parser import UIElement

logger = logging.getLogger(__name__)

class RealOmniParser:
    """真实的OmniParser集成类"""
    
    def __init__(self):
        self.device = OMNIPARSER_CONFIG["device"]
        self.confidence_threshold = OMNIPARSER_CONFIG["confidence_threshold"]
        self.iou_threshold = OMNIPARSER_CONFIG["iou_threshold"]
        
        self.icon_detect_model = None
        self.icon_caption_model = None
        self.model_loaded = False
        
        # 添加OmniParser路径
        self.omniparser_path = Path("omniparser_repo")
        if self.omniparser_path.exists():
            sys.path.insert(0, str(self.omniparser_path))
    
    def load_models(self):
        """加载OmniParser模型"""
        try:
            logger.info("开始加载OmniParser模型...")
            
            # 检查模型文件
            icon_detect_path = Path(OMNIPARSER_CONFIG["icon_detect_model"])
            icon_caption_path = Path(OMNIPARSER_CONFIG["icon_caption_model"])
            
            if not icon_detect_path.exists():
                logger.error(f"图标检测模型不存在: {icon_detect_path}")
                return False
            
            if not icon_caption_path.exists():
                logger.error(f"图标描述模型不存在: {icon_caption_path}")
                return False
            
            # 加载图标检测模型 (YOLO)
            self._load_icon_detection_model(icon_detect_path)
            
            # 加载图标描述模型 (Florence)
            self._load_icon_caption_model(icon_caption_path)
            
            self.model_loaded = True
            logger.info("OmniParser模型加载完成")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def _load_icon_detection_model(self, model_path: Path):
        """加载图标检测模型"""
        try:
            # 尝试导入YOLO
            try:
                from ultralytics import YOLO
                self.icon_detect_model = YOLO(str(model_path))
                logger.info("YOLO图标检测模型加载成功")
            except ImportError:
                logger.warning("ultralytics未安装，使用备用检测方法")
                self.icon_detect_model = self._create_fallback_detector()
                
        except Exception as e:
            logger.error(f"图标检测模型加载失败: {e}")
            self.icon_detect_model = self._create_fallback_detector()
    
    def _load_icon_caption_model(self, model_path: Path):
        """加载图标描述模型"""
        try:
            # 尝试导入transformers
            try:
                from transformers import AutoProcessor, AutoModelForCausalLM
                
                self.icon_caption_processor = AutoProcessor.from_pretrained(str(model_path))
                self.icon_caption_model = AutoModelForCausalLM.from_pretrained(
                    str(model_path),
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                    device_map=self.device if self.device == "cuda" else "cpu"
                )
                logger.info("Florence图标描述模型加载成功")
                
            except ImportError:
                logger.warning("transformers未安装，使用备用描述方法")
                self.icon_caption_model = self._create_fallback_captioner()
                
        except Exception as e:
            logger.error(f"图标描述模型加载失败: {e}")
            self.icon_caption_model = self._create_fallback_captioner()
    
    def _create_fallback_detector(self):
        """创建备用检测器"""
        class FallbackDetector:
            def predict(self, image, conf=0.5, iou=0.45):
                # 简单的边缘检测作为备用
                gray = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                results = []
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if w > 20 and h > 20:  # 过滤小的检测框
                        results.append({
                            'bbox': [x, y, x+w, y+h],
                            'confidence': 0.7,
                            'class': 'ui_element'
                        })
                
                return [type('Result', (), {'boxes': type('Boxes', (), {
                    'xyxy': torch.tensor([r['bbox'] for r in results]),
                    'conf': torch.tensor([r['confidence'] for r in results])
                })()})]
        
        return FallbackDetector()
    
    def _create_fallback_captioner(self):
        """创建备用描述器"""
        class FallbackCaptioner:
            def generate_caption(self, image_crop):
                # 基于图像特征的简单描述
                gray = cv2.cvtColor(np.array(image_crop), cv2.COLOR_RGB2GRAY)
                height, width = gray.shape
                
                # 简单的启发式规则
                if width > height * 2:
                    return "文本框"
                elif abs(width - height) < 10:
                    return "按钮"
                elif height > width:
                    return "垂直控件"
                else:
                    return "UI元素"
        
        return FallbackCaptioner()
    
    def parse_screen(self, image: Image.Image) -> List[UIElement]:
        """解析屏幕图像"""
        try:
            if not self.model_loaded:
                logger.warning("模型未加载，尝试加载...")
                if not self.load_models():
                    logger.error("模型加载失败，使用备用方法")
                    return self._fallback_parse(image)
            
            # 图标检测
            detections = self._detect_icons(image)
            
            # 生成UI元素
            ui_elements = []
            for detection in detections:
                element = self._create_ui_element(image, detection)
                if element:
                    ui_elements.append(element)
            
            logger.info(f"检测到 {len(ui_elements)} 个UI元素")
            return ui_elements
            
        except Exception as e:
            logger.error(f"屏幕解析失败: {e}")
            return self._fallback_parse(image)
    
    def _detect_icons(self, image: Image.Image) -> List[Dict]:
        """检测图标"""
        try:
            # 使用YOLO模型检测
            results = self.icon_detect_model.predict(
                image, 
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                verbose=False
            )
            
            detections = []
            if results and len(results) > 0:
                boxes = results[0].boxes
                if boxes is not None:
                    for i in range(len(boxes.xyxy)):
                        x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()
                        confidence = boxes.conf[i].cpu().numpy()
                        
                        detections.append({
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence)
                        })
            
            return detections
            
        except Exception as e:
            logger.error(f"图标检测失败: {e}")
            return []
    
    def _create_ui_element(self, image: Image.Image, detection: Dict) -> Optional[UIElement]:
        """创建UI元素"""
        try:
            bbox = detection['bbox']
            x1, y1, x2, y2 = bbox
            
            # 裁剪图像区域
            image_crop = image.crop((x1, y1, x2, y2))
            
            # 生成描述
            description = self._generate_description(image_crop)
            
            # 判断是否可交互
            is_interactable = self._is_interactable(image_crop, description)
            
            # 创建UI元素
            element = UIElement(
                x=x1,
                y=y1,
                width=x2 - x1,
                height=y2 - y1,
                center_x=(x1 + x2) // 2,
                center_y=(y1 + y2) // 2,
                confidence=detection['confidence'],
                label="ui_element",
                description=description,
                is_interactable=is_interactable
            )
            
            return element
            
        except Exception as e:
            logger.error(f"UI元素创建失败: {e}")
            return None
    
    def _generate_description(self, image_crop: Image.Image) -> str:
        """生成元素描述"""
        try:
            if hasattr(self.icon_caption_model, 'generate_caption'):
                return self.icon_caption_model.generate_caption(image_crop)
            else:
                # 使用Florence模型生成描述
                prompt = "描述这个UI元素"
                
                inputs = self.icon_caption_processor(
                    text=prompt,
                    images=image_crop,
                    return_tensors="pt"
                ).to(self.device)
                
                with torch.no_grad():
                    generated_ids = self.icon_caption_model.generate(
                        **inputs,
                        max_new_tokens=50,
                        do_sample=False
                    )
                
                description = self.icon_caption_processor.decode(
                    generated_ids[0][inputs['input_ids'].shape[1]:],
                    skip_special_tokens=True
                )
                
                return description.strip()
                
        except Exception as e:
            logger.error(f"描述生成失败: {e}")
            return "UI元素"
    
    def _is_interactable(self, image_crop: Image.Image, description: str) -> bool:
        """判断元素是否可交互"""
        # 基于描述的启发式规则
        interactive_keywords = [
            '按钮', 'button', '输入框', 'textbox', 'input',
            '链接', 'link', '菜单', 'menu', '选项', 'option',
            '复选框', 'checkbox', '单选框', 'radio'
        ]
        
        description_lower = description.lower()
        for keyword in interactive_keywords:
            if keyword in description_lower:
                return True
        
        # 基于图像特征的判断
        try:
            gray = cv2.cvtColor(np.array(image_crop), cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_ratio = np.sum(edges > 0) / edges.size
            
            # 如果边缘比例较高，可能是可交互元素
            return edge_ratio > 0.1
            
        except:
            return True  # 默认认为可交互
    
    def _fallback_parse(self, image: Image.Image) -> List[UIElement]:
        """备用解析方法"""
        logger.info("使用备用解析方法")
        
        # 转换为OpenCV格式
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        
        # 使用模板匹配和边缘检测
        elements = []
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for i, contour in enumerate(contours):
            x, y, w, h = cv2.boundingRect(contour)
            
            # 过滤太小的区域
            if w < 20 or h < 20:
                continue
            
            # 过滤太大的区域（可能是背景）
            if w > image.width * 0.8 or h > image.height * 0.8:
                continue
            
            # 创建UI元素
            element = UIElement(
                x=x,
                y=y,
                width=w,
                height=h,
                center_x=x + w // 2,
                center_y=y + h // 2,
                confidence=0.6,
                label="detected_element",
                description=f"检测元素_{i+1}",
                is_interactable=True
            )
            
            elements.append(element)
        
        return elements[:20]  # 限制数量

# 使用示例
if __name__ == "__main__":
    parser = RealOmniParser()
    
    # 测试模型加载
    if parser.load_models():
        print("✅ 模型加载成功")
        
        # 测试图像解析
        from PIL import Image
        import pyautogui
        
        # 截图测试
        screenshot = pyautogui.screenshot()
        elements = parser.parse_screen(screenshot)
        
        print(f"检测到 {len(elements)} 个UI元素:")
        for i, element in enumerate(elements[:5]):  # 只显示前5个
            print(f"{i+1}. {element.description} - 位置: ({element.center_x}, {element.center_y})")
    else:
        print("❌ 模型加载失败")
