# OmniParser 自动化工具 Makefile

.PHONY: help install test clean build package deploy docs all

# 默认目标
help:
	@echo "OmniParser 自动化工具构建系统"
	@echo ""
	@echo "可用命令:"
	@echo "  install    - 安装依赖包"
	@echo "  setup      - 项目初始化设置"
	@echo "  test       - 运行测试"
	@echo "  clean      - 清理构建文件"
	@echo "  build      - 构建项目"
	@echo "  package    - 创建分发包"
	@echo "  docs       - 生成文档"
	@echo "  deploy     - 部署项目"
	@echo "  all        - 执行完整构建流程"
	@echo ""

# 安装依赖
install:
	@echo "安装项目依赖..."
	python -m pip install --upgrade pip
	python -m pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# 项目设置
setup:
	@echo "项目初始化设置..."
	python setup.py
	@echo "✅ 项目设置完成"

# 运行测试
test:
	@echo "运行测试..."
	python test_automation.py
	@echo "✅ 测试完成"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	python build_tools.py clean
	@if exist __pycache__ rmdir /s /q __pycache__
	@if exist *.pyc del *.pyc
	@if exist .pytest_cache rmdir /s /q .pytest_cache
	@echo "✅ 清理完成"

# 构建项目
build:
	@echo "构建项目..."
	python build_tools.py all
	@echo "✅ 构建完成"

# 创建分发包
package:
	@echo "创建分发包..."
	python build_tools.py zip
	@echo "✅ 分发包创建完成"

# 生成文档
docs:
	@echo "生成文档..."
	python build_tools.py docs
	@echo "✅ 文档生成完成"

# 部署项目
deploy: clean install test build
	@echo "部署项目..."
	@echo "✅ 部署完成"

# 完整构建流程
all: clean install setup test build docs
	@echo "完整构建流程完成"
	@echo ""
	@echo "生成的文件位于 dist/ 目录中"
	@echo "请查看 dist/ 目录获取分发包"

# 开发环境设置
dev-setup:
	@echo "设置开发环境..."
	python -m pip install --upgrade pip
	python -m pip install -r requirements.txt
	python -m pip install pytest black flake8 mypy
	@echo "✅ 开发环境设置完成"

# 代码格式化
format:
	@echo "格式化代码..."
	python -m black *.py
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	@echo "检查代码质量..."
	python -m flake8 *.py --max-line-length=100 --ignore=E203,W503
	@echo "✅ 代码检查完成"

# 类型检查
typecheck:
	@echo "类型检查..."
	python -m mypy *.py --ignore-missing-imports
	@echo "✅ 类型检查完成"

# 快速测试
quick-test:
	@echo "快速测试..."
	python main.py test
	@echo "✅ 快速测试完成"

# 运行示例
examples:
	@echo "运行示例..."
	python examples.py
	@echo "✅ 示例运行完成"

# 启动GUI
gui:
	@echo "启动图形界面..."
	python gui.py

# 启动CLI
cli:
	@echo "启动命令行界面..."
	python main.py interactive

# 性能测试
benchmark:
	@echo "性能测试..."
	python -c "from performance_optimizer import optimizer; optimizer.start_optimization(); print('性能优化器测试完成')"
	@echo "✅ 性能测试完成"

# 安全检查
security:
	@echo "安全检查..."
	python -m pip install bandit
	python -m bandit -r . -f json -o security_report.json
	@echo "✅ 安全检查完成，报告保存在 security_report.json"

# 依赖检查
deps-check:
	@echo "检查依赖..."
	python -m pip check
	python -m pip list --outdated
	@echo "✅ 依赖检查完成"

# 创建虚拟环境
venv:
	@echo "创建虚拟环境..."
	python -m venv venv
	@echo "✅ 虚拟环境创建完成"
	@echo "激活命令: venv\\Scripts\\activate (Windows) 或 source venv/bin/activate (Linux/Mac)"

# 更新依赖
update-deps:
	@echo "更新依赖..."
	python -m pip install --upgrade -r requirements.txt
	@echo "✅ 依赖更新完成"

# 生成需求文件
freeze:
	@echo "生成当前环境的需求文件..."
	python -m pip freeze > requirements_freeze.txt
	@echo "✅ 需求文件已生成: requirements_freeze.txt"

# 检查项目健康状态
health-check: deps-check lint typecheck test
	@echo "项目健康检查完成"

# 发布准备
release-prep: clean health-check build docs
	@echo "发布准备完成"
	@echo "请检查 dist/ 目录中的文件"

# 本地安装
install-local:
	@echo "本地安装..."
	python -m pip install -e .
	@echo "✅ 本地安装完成"

# 卸载
uninstall:
	@echo "卸载..."
	python -m pip uninstall omniparser -y
	@echo "✅ 卸载完成"

# 显示项目信息
info:
	@echo "项目信息:"
	@echo "  名称: OmniParser 自动化工具"
	@echo "  版本: 1.0.0"
	@echo "  Python版本: $(shell python --version)"
	@echo "  项目目录: $(shell cd)"
	@echo "  依赖数量: $(shell python -c "import pkg_resources; print(len(list(pkg_resources.working_set)))")"

# 监控文件变化（需要安装watchdog）
watch:
	@echo "监控文件变化..."
	python -c "import time; from watchdog.observers import Observer; from watchdog.events import FileSystemEventHandler; print('监控启动，按Ctrl+C停止'); time.sleep(3600)"

# 生成变更日志
changelog:
	@echo "生成变更日志..."
	@echo "# 变更日志" > CHANGELOG.md
	@echo "" >> CHANGELOG.md
	@echo "## v1.0.0 - $(shell date /t)" >> CHANGELOG.md
	@echo "- 初始版本发布" >> CHANGELOG.md
	@echo "- 集成OmniParser屏幕解析" >> CHANGELOG.md
	@echo "- 支持自然语言指令" >> CHANGELOG.md
	@echo "- 提供GUI和CLI界面" >> CHANGELOG.md
	@echo "✅ 变更日志已生成: CHANGELOG.md"
