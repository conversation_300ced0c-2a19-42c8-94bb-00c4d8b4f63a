[metadata]
name = omniparser-automation
version = 1.0.0
author = AI Assistant
author_email = <EMAIL>
description = 基于OmniParser的智能电脑自动化操作工具
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/your-username/omniparser-automation
project_urls =
    Bug Tracker = https://github.com/your-username/omniparser-automation/issues
    Documentation = https://github.com/your-username/omniparser-automation/wiki
    Source Code = https://github.com/your-username/omniparser-automation
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: End Users/Desktop
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Operating System :: Microsoft :: Windows
    Operating System :: POSIX :: Linux
    Operating System :: MacOS
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Desktop Environment
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: System :: Systems Administration
    Topic :: Utilities

[options]
packages = find:
python_requires = >=3.8
install_requires =
    torch>=2.0.0
    torchvision>=0.15.0
    transformers>=4.30.0
    Pillow>=9.0.0
    opencv-python>=4.8.0
    numpy>=1.24.0
    ultralytics>=8.0.0
    pyautogui>=0.9.54
    pynput>=1.7.6
    keyboard>=0.13.5
    mouse>=0.7.1
    openai>=1.0.0
    anthropic>=0.7.0
    requests>=2.31.0
    python-dotenv>=1.0.0
    click>=8.1.0
    rich>=13.0.0
    pydantic>=2.0.0
    matplotlib>=3.7.0
    scikit-image>=0.21.0
    psutil>=5.9.0
    weakref

[options.extras_require]
gui = 
    customtkinter>=5.0.0
    tkinter-tooltip>=2.0.0
dev = 
    pytest>=7.0.0
    black>=22.0.0
    flake8>=5.0.0
    mypy>=1.0.0
    bandit>=1.7.0
build = 
    PyInstaller>=5.0.0
    wheel>=0.38.0
    setuptools>=65.0.0
all = 
    %(gui)s
    %(dev)s
    %(build)s

[options.entry_points]
console_scripts =
    omniparser = main:cli
    omniparser-gui = gui:main
    omniparser-run = run:main

[options.package_data]
* = *.txt, *.md, *.json, *.yaml, *.yml, *.cfg, *.ini

[flake8]
max-line-length = 100
ignore = E203, W503, E501
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    venv,
    .venv

[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
ignore_missing_imports = True

[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short

[bdist_wheel]
universal = 0

[egg_info]
tag_build = 
tag_date = 0
