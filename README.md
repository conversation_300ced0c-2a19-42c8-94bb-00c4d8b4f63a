# OmniParser 自动化操作工具

基于微软 OmniParser 的智能电脑自动化操作工具，支持自然语言指令控制电脑操作。

## 功能特性

- 🖥️ **屏幕解析**: 使用 OmniParser 智能识别屏幕上的可交互元素
- 🗣️ **自然语言**: 支持中文自然语言指令，如"点击开始按钮"、"在搜索框中输入文本"
- 🤖 **AI 增强**: 集成 OpenAI GPT 和 Anthropic Claude 进行复杂指令解析
- 🎯 **精确操作**: 自动定位元素并执行鼠标点击、键盘输入等操作
- 📸 **可视化**: 自动生成标注截图，显示识别的元素
- 🔄 **交互模式**: 支持命令行和交互式操作模式

## 安装和设置

### 1. 克隆项目

```bash
git clone <your-repo-url>
cd omniparser
```

### 2. 自动设置

```bash
python setup.py
```

或者手动设置：

```bash
# 安装依赖
pip install -r requirements.txt

# 下载 OmniParser 模型权重
huggingface-cli download microsoft/OmniParser-v2.0 icon_detect/model.pt --local-dir weights
huggingface-cli download microsoft/OmniParser-v2.0 icon_caption/config.json --local-dir weights
# ... 其他模型文件

# 重命名模型目录
mv weights/icon_caption weights/icon_caption_florence
```

### 3. 配置环境变量

编辑 `.env` 文件，添加您的 API 密钥：

```env
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
CUDA_AVAILABLE=false
```

## 使用方法

### 命令行模式

```bash
# 执行单个指令
python main.py run "点击开始按钮"

# 预览模式（不执行实际操作）
python main.py run "在搜索框中输入'hello world'" --preview

# 不保存标注截图
python main.py run "向下滚动3次" --no-screenshot
```

### 交互模式

```bash
python main.py interactive
```

然后输入自然语言指令：
```
请输入指令: 点击开始按钮
请输入指令: 在用户名输入框中输入'admin'
请输入指令: 双击桌面上的文件夹
请输入指令: quit  # 退出
```

### 系统测试

```bash
# 测试系统功能
python main.py test

# 查看帮助
python main.py --help
```

## 支持的指令类型

### 鼠标操作
- `点击开始按钮`
- `双击文件夹图标`
- `右键点击桌面`
- `点击坐标(100, 200)`

### 键盘输入
- `在搜索框中输入'hello world'`
- `输入用户名'admin'`
- `在文本框中打字'测试内容'`

### 滚动操作
- `向下滚动`
- `向上滚动3次`
- `向左滚动`

### 等待操作
- `等待2秒`
- `暂停1.5秒`

### 截图操作
- `截图`
- `保存屏幕截图`

## 项目结构

```
omniparser/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── screen_parser.py       # 屏幕解析模块
├── instruction_parser.py  # 指令解析模块
├── automation_executor.py # 自动化执行模块
├── setup.py              # 安装设置脚本
├── requirements.txt      # 依赖包列表
├── README.md            # 说明文档
├── .env                 # 环境变量配置
├── weights/             # 模型权重文件
├── screenshots/         # 截图保存目录
└── logs/               # 日志文件目录
```

## 配置说明

### 自动化配置 (config.py)

```python
AUTOMATION_CONFIG = {
    "click_delay": 0.5,      # 点击后延迟时间（秒）
    "type_delay": 0.1,       # 打字间隔时间（秒）
    "scroll_speed": 3,       # 滚动速度
    "safety_margin": 10      # 点击位置的安全边距（像素）
}
```

### AI 模型配置

```python
AI_CONFIG = {
    "default_model": "gpt-4-vision-preview",
    "max_tokens": 1000,
    "temperature": 0.1
}
```

## 注意事项

1. **安全性**: 程序具有控制电脑的能力，请谨慎使用
2. **权限**: 某些操作可能需要管理员权限
3. **屏幕分辨率**: 确保屏幕分辨率适中，过高或过低可能影响识别效果
4. **语言支持**: 主要支持中文指令，英文指令也部分支持
5. **模型依赖**: 需要下载 OmniParser 模型文件，首次使用需要联网

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查 `weights/` 目录是否存在模型文件
   - 运行 `python setup.py` 重新下载模型

2. **截图失败**
   - 检查是否有屏幕截图权限
   - 在 macOS 上需要在系统偏好设置中授权

3. **元素识别不准确**
   - 尝试调整 `confidence_threshold` 参数
   - 确保屏幕内容清晰可见

4. **API 调用失败**
   - 检查 `.env` 文件中的 API 密钥是否正确
   - 确保网络连接正常

### 日志查看

```bash
# 查看日志文件
tail -f logs/automation.log
```

## 开发和扩展

### 添加新的操作类型

1. 在 `instruction_parser.py` 中添加新的 `ActionType`
2. 在 `automation_executor.py` 中实现对应的执行方法
3. 在 `instruction_parser.py` 中添加解析规则

### 自定义 UI 元素识别

修改 `screen_parser.py` 中的 `_simulate_parsing` 方法，或集成真实的 OmniParser 模型。

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 致谢

- [Microsoft OmniParser](https://github.com/microsoft/OmniParser) - 核心屏幕解析技术
- [PyAutoGUI](https://github.com/asweigart/pyautogui) - 自动化操作库
- [Rich](https://github.com/Textualize/rich) - 命令行界面美化
