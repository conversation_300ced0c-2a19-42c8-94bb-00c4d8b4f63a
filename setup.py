"""
项目安装和设置脚本
"""
import os
import sys
import subprocess
import urllib.request
from pathlib import Path

def run_command(command, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    print(f"Python版本检查通过: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    print("安装项目依赖...")
    return run_command(f"{sys.executable} -m pip install -r requirements.txt")

def clone_omniparser():
    """克隆OmniParser仓库"""
    omniparser_dir = Path("omniparser_repo")
    if omniparser_dir.exists():
        print("OmniParser仓库已存在，跳过克隆")
        return True
    
    print("克隆OmniParser仓库...")
    return run_command("git clone https://github.com/microsoft/OmniParser.git omniparser_repo")

def download_model_weights():
    """下载模型权重文件"""
    weights_dir = Path("weights")
    weights_dir.mkdir(exist_ok=True)
    
    print("下载模型权重文件...")
    
    # 检查是否已安装huggingface-cli
    if not run_command("huggingface-cli --version", check=False):
        print("安装huggingface_hub...")
        if not run_command(f"{sys.executable} -m pip install huggingface_hub"):
            return False
    
    # 下载模型权重
    model_files = [
        "icon_detect/train_args.yaml",
        "icon_detect/model.pt", 
        "icon_detect/model.yaml",
        "icon_caption/config.json",
        "icon_caption/generation_config.json", 
        "icon_caption/model.safetensors"
    ]
    
    for file_path in model_files:
        cmd = f"huggingface-cli download microsoft/OmniParser-v2.0 {file_path} --local-dir weights"
        if not run_command(cmd):
            print(f"下载 {file_path} 失败")
            return False
    
    # 重命名icon_caption目录
    old_path = weights_dir / "icon_caption"
    new_path = weights_dir / "icon_caption_florence"
    if old_path.exists() and not new_path.exists():
        old_path.rename(new_path)
        print("重命名icon_caption目录为icon_caption_florence")
    
    return True

def create_env_file():
    """创建环境变量文件"""
    env_file = Path(".env")
    if env_file.exists():
        print(".env文件已存在，跳过创建")
        return True
    
    env_content = """# API Keys (请填入您的API密钥)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 系统配置
CUDA_AVAILABLE=false
LOG_LEVEL=INFO
"""
    
    with open(env_file, "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("已创建.env文件，请填入您的API密钥")
    return True

def setup_project():
    """完整的项目设置流程"""
    print("开始设置OmniParser自动化项目...")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 创建目录结构
    from config import create_directories
    create_directories()
    print("项目目录结构创建完成")
    
    # 安装依赖
    if not install_requirements():
        print("依赖安装失败")
        return False
    
    # 克隆OmniParser仓库
    if not clone_omniparser():
        print("OmniParser仓库克隆失败")
        return False
    
    # 下载模型权重
    if not download_model_weights():
        print("模型权重下载失败")
        return False
    
    # 创建环境文件
    if not create_env_file():
        print("环境文件创建失败")
        return False
    
    print("\n✅ 项目设置完成！")
    print("\n下一步:")
    print("1. 编辑.env文件，填入您的API密钥")
    print("2. 运行 python main.py --help 查看使用说明")
    print("3. 运行 python main.py \"点击开始按钮\" 开始自动化操作")
    
    return True

if __name__ == "__main__":
    success = setup_project()
    sys.exit(0 if success else 1)
