"""
指令解析模块 - 将自然语言转换为具体操作指令
"""
import re
import logging
from typing import List, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum

import openai
from anthropic import Anthropic

from config import AI_CONFIG
from screen_parser import UIElement

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ActionType(Enum):
    """操作类型枚举"""
    CLICK = "click"
    DOUBLE_CLICK = "double_click"
    RIGHT_CLICK = "right_click"
    TYPE = "type"
    SCROLL = "scroll"
    DRAG = "drag"
    WAIT = "wait"
    SCREENSHOT = "screenshot"

@dataclass
class Action:
    """操作指令数据类"""
    action_type: ActionType
    target: Optional[str] = None  # 目标元素描述
    text: Optional[str] = None    # 要输入的文本
    coordinates: Optional[tuple] = None  # 坐标 (x, y)
    duration: Optional[float] = None     # 持续时间
    direction: Optional[str] = None      # 方向 (up, down, left, right)
    distance: Optional[int] = None       # 距离

class InstructionParser:
    """指令解析器类"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        
        # 初始化AI客户端
        if AI_CONFIG["openai_api_key"]:
            openai.api_key = AI_CONFIG["openai_api_key"]
            self.openai_client = openai
        
        if AI_CONFIG["anthropic_api_key"]:
            self.anthropic_client = Anthropic(api_key=AI_CONFIG["anthropic_api_key"])
        
        # 预定义的操作模式
        self.action_patterns = {
            r'点击|单击|click': ActionType.CLICK,
            r'双击|double.?click': ActionType.DOUBLE_CLICK,
            r'右击|右键|right.?click': ActionType.RIGHT_CLICK,
            r'输入|打字|type|input': ActionType.TYPE,
            r'滚动|scroll': ActionType.SCROLL,
            r'拖拽|拖动|drag': ActionType.DRAG,
            r'等待|wait|pause': ActionType.WAIT,
            r'截图|screenshot': ActionType.SCREENSHOT
        }
        
        # 预定义的目标元素关键词
        self.element_keywords = {
            '按钮': ['按钮', 'button', '确定', '取消', '开始', '停止'],
            '输入框': ['输入框', 'textbox', 'input', '文本框'],
            '链接': ['链接', 'link', '超链接'],
            '菜单': ['菜单', 'menu', '选项'],
            '图标': ['图标', 'icon'],
            '标签': ['标签', 'tab', '选项卡']
        }
    
    def parse_instruction(self, instruction: str, ui_elements: List[UIElement]) -> List[Action]:
        """解析自然语言指令"""
        try:
            logger.info(f"解析指令: {instruction}")
            
            # 首先尝试使用规则解析
            actions = self._parse_with_rules(instruction, ui_elements)
            
            # 如果规则解析失败，尝试使用AI解析
            if not actions and (self.openai_client or self.anthropic_client):
                actions = self._parse_with_ai(instruction, ui_elements)
            
            logger.info(f"解析出 {len(actions)} 个操作")
            return actions
            
        except Exception as e:
            logger.error(f"指令解析失败: {e}")
            return []
    
    def _parse_with_rules(self, instruction: str, ui_elements: List[UIElement]) -> List[Action]:
        """使用规则解析指令"""
        actions = []
        instruction_lower = instruction.lower()
        
        # 识别操作类型
        action_type = None
        for pattern, act_type in self.action_patterns.items():
            if re.search(pattern, instruction_lower):
                action_type = act_type
                break
        
        if not action_type:
            logger.warning("无法识别操作类型")
            return actions
        
        # 根据操作类型解析参数
        if action_type in [ActionType.CLICK, ActionType.DOUBLE_CLICK, ActionType.RIGHT_CLICK]:
            target = self._extract_target(instruction, ui_elements)
            if target:
                actions.append(Action(action_type=action_type, target=target))
        
        elif action_type == ActionType.TYPE:
            text = self._extract_text_to_type(instruction)
            target = self._extract_target(instruction, ui_elements)
            actions.append(Action(action_type=action_type, text=text, target=target))
        
        elif action_type == ActionType.SCROLL:
            direction, distance = self._extract_scroll_params(instruction)
            actions.append(Action(action_type=action_type, direction=direction, distance=distance))
        
        elif action_type == ActionType.WAIT:
            duration = self._extract_wait_duration(instruction)
            actions.append(Action(action_type=action_type, duration=duration))
        
        elif action_type == ActionType.SCREENSHOT:
            actions.append(Action(action_type=action_type))
        
        return actions
    
    def _extract_target(self, instruction: str, ui_elements: List[UIElement]) -> Optional[str]:
        """提取目标元素"""
        instruction_lower = instruction.lower()
        
        # 查找引号中的内容
        quoted_matches = re.findall(r'["""\'](.*?)["""\']', instruction)
        if quoted_matches:
            return quoted_matches[0]
        
        # 查找常见的UI元素关键词
        for element_type, keywords in self.element_keywords.items():
            for keyword in keywords:
                if keyword in instruction_lower:
                    # 尝试找到包含该关键词的UI元素
                    for element in ui_elements:
                        if keyword in element.description.lower():
                            return element.description
                    return keyword
        
        # 查找"第X个"模式
        number_match = re.search(r'第(\d+)个', instruction)
        if number_match:
            index = int(number_match.group(1)) - 1
            if 0 <= index < len(ui_elements):
                return ui_elements[index].description
        
        return None
    
    def _extract_text_to_type(self, instruction: str) -> Optional[str]:
        """提取要输入的文本"""
        # 查找引号中的内容
        quoted_matches = re.findall(r'["""\'](.*?)["""\']', instruction)
        if quoted_matches:
            return quoted_matches[-1]  # 返回最后一个引号内容
        
        # 查找"输入"后面的内容
        type_match = re.search(r'(?:输入|打字|type)\s*[:：]?\s*(.+)', instruction, re.IGNORECASE)
        if type_match:
            return type_match.group(1).strip()
        
        return None
    
    def _extract_scroll_params(self, instruction: str) -> tuple:
        """提取滚动参数"""
        direction = "down"  # 默认向下
        distance = 3        # 默认距离
        
        if re.search(r'向?上|up', instruction, re.IGNORECASE):
            direction = "up"
        elif re.search(r'向?下|down', instruction, re.IGNORECASE):
            direction = "down"
        elif re.search(r'向?左|left', instruction, re.IGNORECASE):
            direction = "left"
        elif re.search(r'向?右|right', instruction, re.IGNORECASE):
            direction = "right"
        
        # 提取距离数字
        distance_match = re.search(r'(\d+)', instruction)
        if distance_match:
            distance = int(distance_match.group(1))
        
        return direction, distance
    
    def _extract_wait_duration(self, instruction: str) -> float:
        """提取等待时间"""
        # 查找数字
        duration_match = re.search(r'(\d+(?:\.\d+)?)', instruction)
        if duration_match:
            return float(duration_match.group(1))
        
        return 1.0  # 默认等待1秒
    
    def _parse_with_ai(self, instruction: str, ui_elements: List[UIElement]) -> List[Action]:
        """使用AI解析指令"""
        try:
            # 构建UI元素描述
            elements_desc = "\n".join([
                f"{i+1}. {elem.description} - 位置: ({elem.center_x}, {elem.center_y})"
                for i, elem in enumerate(ui_elements)
            ])
            
            prompt = f"""
请将以下自然语言指令转换为具体的操作步骤。

用户指令: {instruction}

当前屏幕上的UI元素:
{elements_desc}

请返回JSON格式的操作列表，每个操作包含以下字段:
- action_type: 操作类型 (click, double_click, right_click, type, scroll, drag, wait, screenshot)
- target: 目标元素描述 (可选)
- text: 要输入的文本 (可选)
- coordinates: 坐标 [x, y] (可选)
- duration: 持续时间 (可选)
- direction: 方向 (可选)
- distance: 距离 (可选)

示例:
[{{"action_type": "click", "target": "开始按钮"}}]
"""
            
            if self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model=AI_CONFIG["default_model"],
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=AI_CONFIG["max_tokens"],
                    temperature=AI_CONFIG["temperature"]
                )
                
                result = response.choices[0].message.content
                return self._parse_ai_response(result)
            
        except Exception as e:
            logger.error(f"AI解析失败: {e}")
        
        return []
    
    def _parse_ai_response(self, response: str) -> List[Action]:
        """解析AI响应"""
        try:
            import json
            
            # 提取JSON部分
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if not json_match:
                return []
            
            json_str = json_match.group(0)
            actions_data = json.loads(json_str)
            
            actions = []
            for action_data in actions_data:
                action_type = ActionType(action_data["action_type"])
                action = Action(
                    action_type=action_type,
                    target=action_data.get("target"),
                    text=action_data.get("text"),
                    coordinates=tuple(action_data["coordinates"]) if action_data.get("coordinates") else None,
                    duration=action_data.get("duration"),
                    direction=action_data.get("direction"),
                    distance=action_data.get("distance")
                )
                actions.append(action)
            
            return actions
            
        except Exception as e:
            logger.error(f"AI响应解析失败: {e}")
            return []

# 使用示例
if __name__ == "__main__":
    parser = InstructionParser()
    
    # 模拟UI元素
    elements = [
        UIElement(x=50, y=50, width=100, height=30, center_x=100, center_y=65,
                 confidence=0.9, label="button", description="开始按钮"),
        UIElement(x=200, y=100, width=150, height=25, center_x=275, center_y=112,
                 confidence=0.8, label="textbox", description="用户名输入框")
    ]
    
    # 测试指令解析
    instructions = [
        "点击开始按钮",
        "在用户名输入框中输入'admin'",
        "向下滚动3次",
        "等待2秒"
    ]
    
    for instruction in instructions:
        actions = parser.parse_instruction(instruction, elements)
        print(f"\n指令: {instruction}")
        for action in actions:
            print(f"  操作: {action}")
