"""
性能优化模块
"""
import time
import threading
import queue
import gc
import psutil
import functools
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import weakref

from logger_config import logger

@dataclass
class PerformanceMetrics:
    """性能指标"""
    cpu_usage: float
    memory_usage: float
    execution_time: float
    cache_hit_rate: float
    error_rate: float
    throughput: float

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.hit_count = 0
        self.miss_count = 0
        
        # 启动清理线程
        self._start_cleanup_thread()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            cache_entry = self.cache[key]
            if time.time() - cache_entry["timestamp"] < self.ttl:
                self.access_times[key] = time.time()
                self.hit_count += 1
                return cache_entry["value"]
            else:
                # 过期，删除
                del self.cache[key]
                del self.access_times[key]
        
        self.miss_count += 1
        return None
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        # 检查大小限制
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[key] = {
            "value": value,
            "timestamp": time.time()
        }
        self.access_times[key] = time.time()
    
    def _evict_lru(self):
        """删除最近最少使用的项"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times, key=self.access_times.get)
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup():
            while True:
                time.sleep(300)  # 每5分钟清理一次
                self._cleanup_expired()
        
        thread = threading.Thread(target=cleanup, daemon=True)
        thread.start()
    
    def _cleanup_expired(self):
        """清理过期项"""
        current_time = time.time()
        expired_keys = []
        
        for key, cache_entry in self.cache.items():
            if current_time - cache_entry["timestamp"] >= self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.hit_count = 0
        self.miss_count = 0

class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
    
    def start_monitoring(self, interval: float = 5.0):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("资源监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("资源监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保持历史记录在合理范围内
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-500:]
                
                # 检查资源使用情况
                self._check_resource_usage(metrics)
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"资源监控错误: {e}")
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        process = psutil.Process()
        
        return PerformanceMetrics(
            cpu_usage=process.cpu_percent(),
            memory_usage=process.memory_percent(),
            execution_time=0.0,  # 由具体操作设置
            cache_hit_rate=0.0,  # 由缓存管理器设置
            error_rate=0.0,      # 由错误处理器设置
            throughput=0.0       # 由具体操作设置
        )
    
    def _check_resource_usage(self, metrics: PerformanceMetrics):
        """检查资源使用情况"""
        if metrics.cpu_usage > 80:
            logger.warning(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")
        
        if metrics.memory_usage > 80:
            logger.warning(f"内存使用率过高: {metrics.memory_usage:.1f}%")
            # 触发垃圾回收
            gc.collect()
    
    def get_average_metrics(self, last_n: int = 10) -> Optional[PerformanceMetrics]:
        """获取平均指标"""
        if not self.metrics_history:
            return None
        
        recent_metrics = self.metrics_history[-last_n:]
        
        return PerformanceMetrics(
            cpu_usage=sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            memory_usage=sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            execution_time=sum(m.execution_time for m in recent_metrics) / len(recent_metrics),
            cache_hit_rate=sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics),
            error_rate=sum(m.error_rate for m in recent_metrics) / len(recent_metrics),
            throughput=sum(m.throughput for m in recent_metrics) / len(recent_metrics)
        )

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.pending_tasks: queue.Queue = queue.Queue()
        self.completed_tasks: List[Any] = []
        self.failed_tasks: List[Any] = []
    
    def submit_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交任务"""
        future = self.executor.submit(func, *args, **kwargs)
        self.pending_tasks.put(future)
        return future
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> List[Any]:
        """等待所有任务完成"""
        futures = []
        
        # 收集所有待处理的任务
        while not self.pending_tasks.empty():
            try:
                future = self.pending_tasks.get_nowait()
                futures.append(future)
            except queue.Empty:
                break
        
        results = []
        for future in as_completed(futures, timeout=timeout):
            try:
                result = future.result()
                results.append(result)
                self.completed_tasks.append(result)
            except Exception as e:
                logger.error(f"任务执行失败: {e}")
                self.failed_tasks.append(e)
        
        return results
    
    def shutdown(self):
        """关闭执行器"""
        self.executor.shutdown(wait=True)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.resource_monitor = ResourceMonitor()
        self.task_queue = TaskQueue()
        
        # 性能配置
        self.enable_caching = True
        self.enable_monitoring = True
        self.enable_parallel_processing = True
        
        # 弱引用池，避免内存泄漏
        self.object_pool = weakref.WeakSet()
    
    def optimize_function(self, cache_key: Optional[str] = None, 
                         monitor_performance: bool = True):
        """函数优化装饰器"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if cache_key and self.enable_caching:
                    key = f"{func.__name__}_{cache_key}"
                    cached_result = self.cache_manager.get(key)
                    if cached_result is not None:
                        return cached_result
                
                # 性能监控
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    
                    # 缓存结果
                    if cache_key and self.enable_caching:
                        self.cache_manager.set(key, result)
                    
                    return result
                    
                finally:
                    if monitor_performance:
                        execution_time = time.time() - start_time
                        if execution_time > 1.0:  # 超过1秒的操作记录警告
                            logger.warning(f"函数 {func.__name__} 执行缓慢: {execution_time:.3f}s")
            
            return wrapper
        return decorator
    
    def batch_process(self, items: List[Any], func: Callable, 
                     batch_size: int = 10) -> List[Any]:
        """批量处理"""
        if not self.enable_parallel_processing:
            return [func(item) for item in items]
        
        results = []
        
        # 分批处理
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            # 并行处理批次
            futures = []
            for item in batch:
                future = self.task_queue.submit_task(func, item)
                futures.append(future)
            
            # 收集结果
            batch_results = []
            for future in futures:
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    batch_results.append(result)
                except Exception as e:
                    logger.error(f"批处理项失败: {e}")
                    batch_results.append(None)
            
            results.extend(batch_results)
        
        return results
    
    def memory_cleanup(self):
        """内存清理"""
        # 清理缓存
        self.cache_manager.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        # 清理对象池
        self.object_pool.clear()
        
        logger.info("内存清理完成")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        avg_metrics = self.resource_monitor.get_average_metrics()
        
        return {
            "cache_hit_rate": self.cache_manager.get_hit_rate(),
            "cache_size": len(self.cache_manager.cache),
            "completed_tasks": len(self.task_queue.completed_tasks),
            "failed_tasks": len(self.task_queue.failed_tasks),
            "average_metrics": avg_metrics.__dict__ if avg_metrics else None,
            "object_pool_size": len(self.object_pool)
        }
    
    def start_optimization(self):
        """启动优化"""
        if self.enable_monitoring:
            self.resource_monitor.start_monitoring()
        
        logger.info("性能优化已启动")
    
    def stop_optimization(self):
        """停止优化"""
        self.resource_monitor.stop_monitoring()
        self.task_queue.shutdown()
        
        logger.info("性能优化已停止")

# 全局优化器实例
optimizer = PerformanceOptimizer()

# 便捷装饰器
def cached(cache_key: str = None):
    """缓存装饰器"""
    return optimizer.optimize_function(cache_key=cache_key)

def monitored(func: Callable) -> Callable:
    """性能监控装饰器"""
    return optimizer.optimize_function(monitor_performance=True)(func)

# 使用示例
if __name__ == "__main__":
    # 启动优化
    optimizer.start_optimization()
    
    # 测试缓存功能
    @cached(cache_key="test")
    def expensive_function(x):
        time.sleep(1)  # 模拟耗时操作
        return x * 2
    
    # 第一次调用
    start = time.time()
    result1 = expensive_function(5)
    time1 = time.time() - start
    
    # 第二次调用（应该从缓存获取）
    start = time.time()
    result2 = expensive_function(5)
    time2 = time.time() - start
    
    print(f"第一次调用: {time1:.3f}s, 结果: {result1}")
    print(f"第二次调用: {time2:.3f}s, 结果: {result2}")
    print(f"缓存命中率: {optimizer.cache_manager.get_hit_rate():.2%}")
    
    # 获取性能报告
    report = optimizer.get_performance_report()
    print("性能报告:", report)
    
    # 停止优化
    optimizer.stop_optimization()
