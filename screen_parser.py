"""
屏幕解析模块 - 使用OmniParser解析屏幕截图
"""
import os
import sys
import time
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pya<PERSON>gui
import torch

from config import OMNIPARSER_CONFIG, SCREENSHOT_CONFIG, AUTOMATION_CONFIG
from omniparser_integration import RealOmniParser

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UIElement:
    """UI元素数据类"""
    x: int
    y: int
    width: int
    height: int
    center_x: int
    center_y: int
    confidence: float
    label: str
    description: str = ""
    is_interactable: bool = True
    element_type: str = "unknown"

class ScreenParser:
    """屏幕解析器类"""
    
    def __init__(self):
        self.device = OMNIPARSER_CONFIG["device"]
        self.confidence_threshold = OMNIPARSER_CONFIG["confidence_threshold"]
        self.iou_threshold = OMNIPARSER_CONFIG["iou_threshold"]
        self.model_loaded = False

        # 使用真实的OmniParser
        self.real_parser = RealOmniParser()
        self.use_real_parser = True  # 可以切换到模拟模式

        # 创建截图保存目录
        SCREENSHOT_CONFIG["save_dir"].mkdir(parents=True, exist_ok=True)
    
    def load_models(self):
        """加载OmniParser模型"""
        try:
            logger.info("加载OmniParser模型...")

            if self.use_real_parser:
                # 使用真实的OmniParser
                success = self.real_parser.load_models()
                if success:
                    self.model_loaded = True
                    logger.info("真实OmniParser模型加载完成")
                else:
                    logger.warning("真实模型加载失败，切换到模拟模式")
                    self.use_real_parser = False
                    self.model_loaded = True
            else:
                # 模拟模式
                logger.info("使用模拟OmniParser模型")
                self.model_loaded = True

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            self.use_real_parser = False
            self.model_loaded = True  # 即使失败也可以使用模拟模式
    
    def take_screenshot(self, save_path: Optional[str] = None) -> str:
        """截取屏幕截图"""
        try:
            # 使用pyautogui截图
            screenshot = pyautogui.screenshot()
            
            # 生成保存路径
            if save_path is None:
                timestamp = int(time.time())
                save_path = SCREENSHOT_CONFIG["save_dir"] / f"screenshot_{timestamp}.png"
            
            # 保存截图
            screenshot.save(save_path, format=SCREENSHOT_CONFIG["format"])
            logger.info(f"截图已保存: {save_path}")
            
            return str(save_path)
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            raise
    
    def parse_screenshot(self, image_path: str) -> List[UIElement]:
        """解析截图，识别UI元素"""
        try:
            if not self.model_loaded:
                self.load_models()

            # 加载图片
            image = Image.open(image_path)

            if self.use_real_parser and self.real_parser.model_loaded:
                # 使用真实的OmniParser
                elements = self.real_parser.parse_screen(image)
            else:
                # 使用模拟实现
                image_array = np.array(image)
                elements = self._simulate_parsing(image_array)

            logger.info(f"识别到 {len(elements)} 个UI元素")
            return elements

        except Exception as e:
            logger.error(f"截图解析失败: {e}")
            return []
    
    def _simulate_parsing(self, image_array: np.ndarray) -> List[UIElement]:
        """模拟解析结果（用于演示）"""
        height, width = image_array.shape[:2]
        
        # 模拟一些常见的UI元素
        elements = [
            UIElement(
                x=50, y=50, width=100, height=30,
                center_x=100, center_y=65,
                confidence=0.9, label="button",
                description="开始按钮", is_interactable=True,
                element_type="button"
            ),
            UIElement(
                x=200, y=100, width=150, height=25,
                center_x=275, center_y=112,
                confidence=0.8, label="textbox",
                description="输入框", is_interactable=True,
                element_type="input"
            ),
            UIElement(
                x=width-120, y=20, width=100, height=30,
                center_x=width-70, center_y=35,
                confidence=0.85, label="button",
                description="关闭按钮", is_interactable=True,
                element_type="button"
            )
        ]
        
        return elements
    
    def annotate_screenshot(self, image_path: str, elements: List[UIElement], 
                          output_path: Optional[str] = None) -> str:
        """在截图上标注识别的元素"""
        try:
            # 加载图片
            image = Image.open(image_path)
            draw = ImageDraw.Draw(image)
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()
            
            # 绘制元素边框和标签
            for i, element in enumerate(elements):
                # 绘制边框
                bbox = [element.x, element.y, 
                       element.x + element.width, 
                       element.y + element.height]
                
                color = "red" if element.is_interactable else "blue"
                draw.rectangle(bbox, outline=color, width=2)
                
                # 绘制标签
                label_text = f"{i+1}: {element.description}"
                text_bbox = draw.textbbox((0, 0), label_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                # 标签背景
                label_bbox = [element.x, element.y - text_height - 5,
                             element.x + text_width + 10, element.y]
                draw.rectangle(label_bbox, fill=color)
                
                # 标签文字
                draw.text((element.x + 5, element.y - text_height - 2), 
                         label_text, fill="white", font=font)
            
            # 保存标注后的图片
            if output_path is None:
                base_path = Path(image_path)
                output_path = base_path.parent / f"{base_path.stem}_annotated{base_path.suffix}"
            
            image.save(output_path)
            logger.info(f"标注图片已保存: {output_path}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"图片标注失败: {e}")
            raise
    
    def find_element_by_description(self, elements: List[UIElement], 
                                  description: str) -> Optional[UIElement]:
        """根据描述查找元素"""
        description_lower = description.lower()
        
        # 精确匹配
        for element in elements:
            if description_lower in element.description.lower():
                return element
        
        # 模糊匹配
        for element in elements:
            if any(word in element.description.lower() 
                  for word in description_lower.split()):
                return element
        
        return None
    
    def get_screen_info(self) -> Dict[str, int]:
        """获取屏幕信息"""
        size = pyautogui.size()
        return {
            "width": size.width,
            "height": size.height
        }

# 使用示例
if __name__ == "__main__":
    parser = ScreenParser()
    
    # 截图并解析
    screenshot_path = parser.take_screenshot()
    elements = parser.parse_screenshot(screenshot_path)
    
    # 标注截图
    annotated_path = parser.annotate_screenshot(screenshot_path, elements)
    
    # 打印识别结果
    print(f"识别到 {len(elements)} 个UI元素:")
    for i, element in enumerate(elements):
        print(f"{i+1}. {element.description} - 位置: ({element.center_x}, {element.center_y})")
