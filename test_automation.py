"""
自动化测试模块
"""
import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from screen_parser import ScreenParser, UIElement
from instruction_parser import InstructionParser, Action, ActionType
from automation_executor import AutomationExecutor

class TestScreenParser(unittest.TestCase):
    """屏幕解析器测试"""
    
    def setUp(self):
        self.parser = ScreenParser()
    
    def test_ui_element_creation(self):
        """测试UI元素创建"""
        element = UIElement(
            x=10, y=20, width=100, height=30,
            center_x=60, center_y=35,
            confidence=0.9, label="button",
            description="测试按钮"
        )
        
        self.assertEqual(element.x, 10)
        self.assertEqual(element.y, 20)
        self.assertEqual(element.center_x, 60)
        self.assertEqual(element.description, "测试按钮")
        self.assertTrue(element.is_interactable)
    
    @patch('pyautogui.screenshot')
    def test_take_screenshot(self, mock_screenshot):
        """测试截图功能"""
        # 模拟截图
        mock_image = Mock()
        mock_screenshot.return_value = mock_image
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置临时目录
            self.parser.screenshot_config = {"save_dir": Path(temp_dir)}
            
            # 执行截图
            result = self.parser.take_screenshot()
            
            # 验证结果
            self.assertTrue(result.endswith('.png'))
            mock_screenshot.assert_called_once()
            mock_image.save.assert_called_once()
    
    def test_find_element_by_description(self):
        """测试根据描述查找元素"""
        elements = [
            UIElement(x=0, y=0, width=50, height=20, center_x=25, center_y=10,
                     confidence=0.9, label="button", description="开始按钮"),
            UIElement(x=100, y=0, width=50, height=20, center_x=125, center_y=10,
                     confidence=0.8, label="button", description="停止按钮"),
            UIElement(x=0, y=50, width=100, height=25, center_x=50, center_y=62,
                     confidence=0.7, label="textbox", description="用户名输入框")
        ]
        
        # 精确匹配
        result = self.parser.find_element_by_description(elements, "开始按钮")
        self.assertIsNotNone(result)
        self.assertEqual(result.description, "开始按钮")
        
        # 模糊匹配
        result = self.parser.find_element_by_description(elements, "用户名")
        self.assertIsNotNone(result)
        self.assertEqual(result.description, "用户名输入框")
        
        # 找不到
        result = self.parser.find_element_by_description(elements, "不存在的元素")
        self.assertIsNone(result)

class TestInstructionParser(unittest.TestCase):
    """指令解析器测试"""
    
    def setUp(self):
        self.parser = InstructionParser()
        self.ui_elements = [
            UIElement(x=0, y=0, width=50, height=20, center_x=25, center_y=10,
                     confidence=0.9, label="button", description="开始按钮"),
            UIElement(x=0, y=50, width=100, height=25, center_x=50, center_y=62,
                     confidence=0.7, label="textbox", description="用户名输入框")
        ]
    
    def test_parse_click_instruction(self):
        """测试点击指令解析"""
        instruction = "点击开始按钮"
        actions = self.parser._parse_with_rules(instruction, self.ui_elements)
        
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0].action_type, ActionType.CLICK)
        self.assertEqual(actions[0].target, "开始按钮")
    
    def test_parse_type_instruction(self):
        """测试输入指令解析"""
        instruction = "在用户名输入框中输入'admin'"
        actions = self.parser._parse_with_rules(instruction, self.ui_elements)
        
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0].action_type, ActionType.TYPE)
        self.assertEqual(actions[0].text, "admin")
        self.assertEqual(actions[0].target, "用户名输入框")
    
    def test_parse_scroll_instruction(self):
        """测试滚动指令解析"""
        instruction = "向下滚动3次"
        actions = self.parser._parse_with_rules(instruction, self.ui_elements)
        
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0].action_type, ActionType.SCROLL)
        self.assertEqual(actions[0].direction, "down")
        self.assertEqual(actions[0].distance, 3)
    
    def test_parse_wait_instruction(self):
        """测试等待指令解析"""
        instruction = "等待2秒"
        actions = self.parser._parse_with_rules(instruction, self.ui_elements)
        
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0].action_type, ActionType.WAIT)
        self.assertEqual(actions[0].duration, 2.0)
    
    def test_extract_text_to_type(self):
        """测试文本提取"""
        # 引号内容
        text = self.parser._extract_text_to_type("输入'hello world'")
        self.assertEqual(text, "hello world")
        
        # 冒号后内容
        text = self.parser._extract_text_to_type("输入：测试文本")
        self.assertEqual(text, "测试文本")
        
        # 无法提取
        text = self.parser._extract_text_to_type("点击按钮")
        self.assertIsNone(text)

class TestAutomationExecutor(unittest.TestCase):
    """自动化执行器测试"""
    
    def setUp(self):
        self.executor = AutomationExecutor()
        self.ui_elements = [
            UIElement(x=100, y=100, width=50, height=20, center_x=125, center_y=110,
                     confidence=0.9, label="button", description="测试按钮")
        ]
    
    @patch('pyautogui.click')
    def test_execute_click(self, mock_click):
        """测试点击执行"""
        action = Action(action_type=ActionType.CLICK, target="测试按钮")
        
        result = self.executor._execute_single_action(action, self.ui_elements)
        
        self.assertTrue(result)
        mock_click.assert_called_once()
        # 验证点击位置在元素范围内
        args = mock_click.call_args[0]
        self.assertGreaterEqual(args[0], 100)  # x >= element.x
        self.assertLessEqual(args[0], 150)     # x <= element.x + width
        self.assertGreaterEqual(args[1], 100)  # y >= element.y
        self.assertLessEqual(args[1], 120)     # y <= element.y + height
    
    @patch('pyautogui.typewrite')
    @patch('pyautogui.click')
    def test_execute_type(self, mock_click, mock_typewrite):
        """测试输入执行"""
        action = Action(action_type=ActionType.TYPE, target="测试按钮", text="hello")
        
        result = self.executor._execute_single_action(action, self.ui_elements)
        
        self.assertTrue(result)
        mock_click.assert_called_once()  # 先点击目标
        mock_typewrite.assert_called_once_with("hello", interval=self.executor.type_delay)
    
    @patch('pyautogui.scroll')
    def test_execute_scroll(self, mock_scroll):
        """测试滚动执行"""
        action = Action(action_type=ActionType.SCROLL, direction="down", distance=3)
        
        result = self.executor._execute_single_action(action, self.ui_elements)
        
        self.assertTrue(result)
        mock_scroll.assert_called_once()
        # 验证滚动参数
        args = mock_scroll.call_args[0]
        self.assertGreater(args[0], 0)  # 向下滚动应该是正数
    
    @patch('time.sleep')
    def test_execute_wait(self, mock_sleep):
        """测试等待执行"""
        action = Action(action_type=ActionType.WAIT, duration=2.5)
        
        result = self.executor._execute_single_action(action, self.ui_elements)
        
        self.assertTrue(result)
        mock_sleep.assert_called_once_with(2.5)
    
    def test_execution_history(self):
        """测试执行历史记录"""
        # 清空历史
        self.executor.clear_execution_history()
        self.assertEqual(len(self.executor.get_execution_history()), 0)
        
        # 模拟执行
        action = Action(action_type=ActionType.WAIT, duration=0.1)
        with patch('time.sleep'):
            self.executor._execute_single_action(action, self.ui_elements)
        
        # 手动添加历史记录（因为我们测试的是单个操作）
        self.executor.execution_history.append({
            "action": action,
            "timestamp": 1234567890,
            "success": True
        })
        
        history = self.executor.get_execution_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]["action"], action)
        self.assertTrue(history[0]["success"])

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        self.screen_parser = ScreenParser()
        self.instruction_parser = InstructionParser()
        self.executor = AutomationExecutor()
    
    @patch('pyautogui.click')
    @patch('pyautogui.screenshot')
    def test_full_workflow(self, mock_screenshot, mock_click):
        """测试完整工作流程"""
        # 模拟截图
        mock_image = Mock()
        mock_screenshot.return_value = mock_image
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置临时目录
            from config import SCREENSHOT_CONFIG
            SCREENSHOT_CONFIG["save_dir"] = Path(temp_dir)
            
            # 1. 截图
            screenshot_path = self.screen_parser.take_screenshot()
            self.assertTrue(screenshot_path.endswith('.png'))
            
            # 2. 解析屏幕（使用模拟数据）
            ui_elements = self.screen_parser._simulate_parsing(None)
            self.assertGreater(len(ui_elements), 0)
            
            # 3. 解析指令
            instruction = "点击开始按钮"
            actions = self.instruction_parser.parse_instruction(instruction, ui_elements)
            self.assertGreater(len(actions), 0)
            
            # 4. 执行操作
            success = self.executor.execute_actions(actions, ui_elements)
            self.assertTrue(success)
            
            # 验证点击被调用
            mock_click.assert_called()

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestScreenParser,
        TestInstructionParser,
        TestAutomationExecutor,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
