"""
配置文件 - 存储项目的各种配置参数
"""
import os
from pathlib import Path
from typing import Dict, Any

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# OmniParser 相关配置
OMNIPARSER_CONFIG = {
    "weights_dir": PROJECT_ROOT / "weights",
    "icon_detect_model": "weights/icon_detect/model.pt",
    "icon_caption_model": "weights/icon_caption_florence",
    "device": "cuda" if os.environ.get("CUDA_AVAILABLE", "false").lower() == "true" else "cpu",
    "confidence_threshold": 0.5,
    "iou_threshold": 0.45
}

# 屏幕截图配置
SCREENSHOT_CONFIG = {
    "save_dir": PROJECT_ROOT / "screenshots",
    "format": "PNG",
    "quality": 95
}

# 自动化操作配置
AUTOMATION_CONFIG = {
    "click_delay": 0.5,  # 点击后延迟时间（秒）
    "type_delay": 0.1,   # 打字间隔时间（秒）
    "scroll_speed": 3,   # 滚动速度
    "double_click_interval": 0.25,  # 双击间隔
    "safety_margin": 10  # 点击位置的安全边距（像素）
}

# AI 模型配置
AI_CONFIG = {
    "openai_api_key": os.getenv("OPENAI_API_KEY"),
    "anthropic_api_key": os.getenv("ANTHROPIC_API_KEY"),
    "default_model": "gpt-4-vision-preview",
    "max_tokens": 1000,
    "temperature": 0.1
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "log_file": PROJECT_ROOT / "logs" / "automation.log"
}

# 创建必要的目录
def create_directories():
    """创建项目所需的目录"""
    directories = [
        SCREENSHOT_CONFIG["save_dir"],
        OMNIPARSER_CONFIG["weights_dir"],
        LOGGING_CONFIG["log_file"].parent
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
    print("项目目录结构创建完成！")
